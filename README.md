# Cellular Dominion

An emergent civilization-building game where simple rules create infinite complexity.

## Overview

Cellular Dominion is a unique civilization-building game where you manage a growing society of autonomous "cells" on an infinite, procedurally-generated world. Each citizen is represented as a colored circle with simple behavioral rules that create emergent complexity through their interactions.

## Key Features

- **Autonomous Cells**: Citizens act independently based on simple rules, creating emergent behaviors
- **Infinite World**: Procedurally generated chunks with diverse biomes and resources
- **Emergent Technology**: Discoveries arise naturally from cell interactions rather than preset tech trees
- **Cultural Evolution**: Your civilization develops unique traits based on your choices
- **Genetic Drift**: Cell populations evolve over generations with environmental pressures
- **Dynamic Challenges**: Rival civilizations, environmental events, and internal politics emerge organically

## Installation

### Prerequisites

- Python 3.9 or higher
- pip (Python package installer)

### Setup

1. Clone the repository:
```bash
git clone https://github.com/yourusername/cellular-dominion.git
cd cellular-dominion
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install the game:
```bash
pip install -e .
```

### Development Setup

For development, install with development dependencies:
```bash
pip install -e ".[dev]"
```

## Running the Game

```bash
cellular-dominion
```

Or run directly with Python:
```bash
python -m cellular_dominion.main
```

## Development

### Project Structure

```
src/cellular_dominion/     # Main source code
├── game/                  # Core game engine
├── entities/              # Game entities (cells, civilizations)
├── world/                 # World generation and management
├── systems/               # Emergent behavior systems
├── rendering/             # Graphics and UI
├── input/                 # Input handling
└── utils/                 # Utility functions

tests/                     # Unit tests
assets/                    # Game assets
docs/                      # Documentation
```

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black src/ tests/
```

### Type Checking

```bash
mypy src/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Game Concept

The game is built around the principle that complex, engaging gameplay can emerge from simple rules. Each cell in your civilization follows basic behavioral patterns, but their interactions create sophisticated emergent behaviors that keep the game fresh and unpredictable.

### Cell Specializations

- **Miners** (Red): Extract resources from the environment
- **Builders** (Blue): Construct buildings and infrastructure
- **Scouts** (Green): Explore new territories
- **Researchers** (Purple): Drive technological advancement
- **Traders** (Yellow): Facilitate resource exchange

### Emergent Systems

The game features several interconnected systems that create emergent gameplay:

- **Technology Discovery**: New capabilities emerge from specific cell interactions
- **Cultural Evolution**: Your civilization develops traits based on your decisions
- **Environmental Adaptation**: Cells evolve to match their environment
- **Political Dynamics**: Factions form naturally as your civilization grows

## Roadmap

- [ ] Core cell behavior system
- [ ] Basic world generation
- [ ] Simple rendering with Pygame
- [ ] Resource management
- [ ] Technology emergence system
- [ ] Cultural evolution mechanics
- [ ] Save/load functionality
- [ ] Advanced AI civilizations
- [ ] Enhanced graphics and UI
- [ ] Sound and music integration
