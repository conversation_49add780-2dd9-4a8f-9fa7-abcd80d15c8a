"""
Serialization utilities for save/load functionality.
"""

import json
import pickle
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, Type, TypeVar
import logging

T = TypeVar('T', bound='Serializable')


class Serializable(ABC):
    """
    Abstract base class for objects that can be serialized.
    
    Provides a common interface for saving and loading game objects.
    """
    
    @abstractmethod
    def serialize(self) -> Dict[str, Any]:
        """
        Serialize the object to a dictionary.
        
        Returns:
            Dictionary containing all necessary data to reconstruct the object
        """
        pass
    
    @classmethod
    @abstractmethod
    def deserialize(cls: Type[T], data: Dict[str, Any]) -> T:
        """
        Create an object from serialized data.
        
        Args:
            data: Dictionary containing serialized object data
            
        Returns:
            Reconstructed object instance
        """
        pass


class SaveManager:
    """
    Manages saving and loading of game state.
    
    Handles different serialization formats and provides
    utilities for managing save files.
    """
    
    def __init__(self, save_directory: Path = Path("saves")):
        self.save_directory = save_directory
        self.save_directory.mkdir(exist_ok=True)
        self.logger = logging.getLogger(__name__)
    
    def save_json(self, data: Dict[str, Any], filename: str) -> bool:
        """
        Save data as JSON file.
        
        Args:
            data: Data to save
            filename: Name of the save file (without extension)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            filepath = self.save_directory / f"{filename}.json"
            
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2, default=self._json_serializer)
            
            self.logger.info(f"Saved game data to {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save JSON data: {e}")
            return False
    
    def load_json(self, filename: str) -> Dict[str, Any]:
        """
        Load data from JSON file.
        
        Args:
            filename: Name of the save file (without extension)
            
        Returns:
            Loaded data dictionary
            
        Raises:
            FileNotFoundError: If save file doesn't exist
            json.JSONDecodeError: If file is corrupted
        """
        filepath = self.save_directory / f"{filename}.json"
        
        if not filepath.exists():
            raise FileNotFoundError(f"Save file not found: {filepath}")
        
        try:
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            self.logger.info(f"Loaded game data from {filepath}")
            return data
            
        except json.JSONDecodeError as e:
            self.logger.error(f"Corrupted save file {filepath}: {e}")
            raise
    
    def save_binary(self, data: Any, filename: str) -> bool:
        """
        Save data as binary pickle file.
        
        Args:
            data: Data to save
            filename: Name of the save file (without extension)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            filepath = self.save_directory / f"{filename}.pkl"
            
            with open(filepath, 'wb') as f:
                pickle.dump(data, f)
            
            self.logger.info(f"Saved binary data to {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save binary data: {e}")
            return False
    
    def load_binary(self, filename: str) -> Any:
        """
        Load data from binary pickle file.
        
        Args:
            filename: Name of the save file (without extension)
            
        Returns:
            Loaded data
            
        Raises:
            FileNotFoundError: If save file doesn't exist
        """
        filepath = self.save_directory / f"{filename}.pkl"
        
        if not filepath.exists():
            raise FileNotFoundError(f"Save file not found: {filepath}")
        
        try:
            with open(filepath, 'rb') as f:
                data = pickle.load(f)
            
            self.logger.info(f"Loaded binary data from {filepath}")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to load binary data: {e}")
            raise
    
    def list_saves(self, extension: str = ".json") -> list[str]:
        """
        List all save files with the given extension.
        
        Args:
            extension: File extension to filter by
            
        Returns:
            List of save file names (without extension)
        """
        saves = []
        for filepath in self.save_directory.glob(f"*{extension}"):
            saves.append(filepath.stem)
        
        return sorted(saves)
    
    def delete_save(self, filename: str, extension: str = ".json") -> bool:
        """
        Delete a save file.
        
        Args:
            filename: Name of the save file (without extension)
            extension: File extension
            
        Returns:
            True if successful, False otherwise
        """
        try:
            filepath = self.save_directory / f"{filename}{extension}"
            
            if filepath.exists():
                filepath.unlink()
                self.logger.info(f"Deleted save file: {filepath}")
                return True
            else:
                self.logger.warning(f"Save file not found: {filepath}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to delete save file: {e}")
            return False
    
    def get_save_info(self, filename: str, extension: str = ".json") -> Dict[str, Any]:
        """
        Get information about a save file.
        
        Args:
            filename: Name of the save file (without extension)
            extension: File extension
            
        Returns:
            Dictionary with file information
        """
        filepath = self.save_directory / f"{filename}{extension}"
        
        if not filepath.exists():
            return {}
        
        stat = filepath.stat()
        
        return {
            "filename": filename,
            "size": stat.st_size,
            "created": stat.st_ctime,
            "modified": stat.st_mtime,
            "exists": True
        }
    
    def _json_serializer(self, obj: Any) -> Any:
        """
        Custom JSON serializer for complex objects.
        
        Handles objects that aren't natively JSON serializable.
        """
        if hasattr(obj, 'serialize'):
            return obj.serialize()
        elif hasattr(obj, '__dict__'):
            return obj.__dict__
        else:
            return str(obj)


class CompressedSaveManager(SaveManager):
    """
    Save manager with compression support for larger save files.
    """
    
    def __init__(self, save_directory: Path = Path("saves")):
        super().__init__(save_directory)
        try:
            import gzip
            self.gzip = gzip
            self.compression_available = True
        except ImportError:
            self.compression_available = False
            self.logger.warning("Compression not available - falling back to uncompressed saves")
    
    def save_compressed_json(self, data: Dict[str, Any], filename: str) -> bool:
        """
        Save data as compressed JSON file.
        """
        if not self.compression_available:
            return self.save_json(data, filename)
        
        try:
            filepath = self.save_directory / f"{filename}.json.gz"
            
            json_data = json.dumps(data, indent=2, default=self._json_serializer)
            
            with self.gzip.open(filepath, 'wt') as f:
                f.write(json_data)
            
            self.logger.info(f"Saved compressed data to {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save compressed data: {e}")
            return False
    
    def load_compressed_json(self, filename: str) -> Dict[str, Any]:
        """
        Load data from compressed JSON file.
        """
        if not self.compression_available:
            return self.load_json(filename)
        
        filepath = self.save_directory / f"{filename}.json.gz"
        
        if not filepath.exists():
            raise FileNotFoundError(f"Compressed save file not found: {filepath}")
        
        try:
            with self.gzip.open(filepath, 'rt') as f:
                data = json.load(f)
            
            self.logger.info(f"Loaded compressed data from {filepath}")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to load compressed data: {e}")
            raise
