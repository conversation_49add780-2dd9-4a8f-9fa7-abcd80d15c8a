"""
Camera system for viewport management and world-to-screen coordinate conversion.
"""

import math
from typing import List, Tu<PERSON>, Dict, Any

from ..utils.math_utils import Vector2, clamp


class Camera:
    """
    Camera system for managing the viewport and coordinate transformations.
    
    Handles panning, zooming, and converting between world coordinates
    and screen coordinates.
    """
    
    def __init__(self, screen_width: int, screen_height: int):
        self.screen_width = screen_width
        self.screen_height = screen_height
        
        # Camera position in world coordinates
        self.position = Vector2(0, 0)
        
        # Zoom level (1.0 = normal, >1.0 = zoomed in, <1.0 = zoomed out)
        self.zoom = 1.0
        self.min_zoom = 0.1
        self.max_zoom = 10.0
        
        # Camera movement
        self.velocity = Vector2(0, 0)
        self.drag = 0.9  # Friction for smooth camera movement
        
        # Smooth zoom
        self.target_zoom = self.zoom
        self.zoom_speed = 5.0
        
        # Viewport bounds (for culling)
        self.viewport_margin = 100  # Extra margin for smooth scrolling
    
    def update(self, delta_time: float) -> None:
        """Update camera position and zoom."""
        # Apply velocity to position
        self.position += self.velocity * delta_time
        
        # Apply drag to velocity
        self.velocity *= self.drag
        
        # Smooth zoom interpolation
        if abs(self.zoom - self.target_zoom) > 0.01:
            zoom_diff = self.target_zoom - self.zoom
            self.zoom += zoom_diff * self.zoom_speed * delta_time
        else:
            self.zoom = self.target_zoom
        
        # Clamp zoom
        self.zoom = clamp(self.zoom, self.min_zoom, self.max_zoom)
        self.target_zoom = clamp(self.target_zoom, self.min_zoom, self.max_zoom)
    
    def move(self, dx: float, dy: float) -> None:
        """Move the camera by the given offset."""
        self.velocity += Vector2(dx, dy)
    
    def set_position(self, x: float, y: float) -> None:
        """Set the camera position directly."""
        self.position = Vector2(x, y)
        self.velocity = Vector2(0, 0)
    
    def zoom_at_point(self, zoom_factor: float, screen_x: int, screen_y: int) -> None:
        """
        Zoom in/out while keeping the specified screen point fixed.
        
        Args:
            zoom_factor: Multiplier for zoom level
            screen_x, screen_y: Screen coordinates to zoom toward
        """
        # Convert screen point to world coordinates before zoom
        world_point = self.screen_to_world(screen_x, screen_y)
        
        # Apply zoom
        self.target_zoom *= zoom_factor
        
        # Convert the same world point back to screen coordinates after zoom
        new_screen_point = self.world_to_screen(world_point.x, world_point.y)
        
        # Adjust camera position to keep the point fixed
        screen_center_x = self.screen_width // 2
        screen_center_y = self.screen_height // 2
        
        offset_x = (new_screen_point[0] - screen_x) / self.zoom
        offset_y = (new_screen_point[1] - screen_y) / self.zoom
        
        self.position.x += offset_x
        self.position.y += offset_y
    
    def zoom(self, zoom_factor: float) -> None:
        """Zoom in/out from the center of the screen."""
        center_x = self.screen_width // 2
        center_y = self.screen_height // 2
        self.zoom_at_point(zoom_factor, center_x, center_y)
    
    def world_to_screen(self, world_x: float, world_y: float) -> Tuple[int, int]:
        """
        Convert world coordinates to screen coordinates.
        
        Args:
            world_x, world_y: World coordinates
            
        Returns:
            Screen coordinates as (x, y) tuple
        """
        # Translate relative to camera position
        relative_x = world_x - self.position.x
        relative_y = world_y - self.position.y
        
        # Apply zoom
        screen_x = relative_x * self.zoom + self.screen_width // 2
        screen_y = relative_y * self.zoom + self.screen_height // 2
        
        return (int(screen_x), int(screen_y))
    
    def screen_to_world(self, screen_x: int, screen_y: int) -> Vector2:
        """
        Convert screen coordinates to world coordinates.
        
        Args:
            screen_x, screen_y: Screen coordinates
            
        Returns:
            World coordinates as Vector2
        """
        # Convert to relative coordinates
        relative_x = (screen_x - self.screen_width // 2) / self.zoom
        relative_y = (screen_y - self.screen_height // 2) / self.zoom
        
        # Translate to world coordinates
        world_x = relative_x + self.position.x
        world_y = relative_y + self.position.y
        
        return Vector2(world_x, world_y)
    
    def get_viewport_bounds(self) -> Tuple[float, float, float, float]:
        """
        Get the world coordinates of the viewport bounds.
        
        Returns:
            (left, top, right, bottom) in world coordinates
        """
        top_left = self.screen_to_world(-self.viewport_margin, -self.viewport_margin)
        bottom_right = self.screen_to_world(
            self.screen_width + self.viewport_margin,
            self.screen_height + self.viewport_margin
        )
        
        return (top_left.x, top_left.y, bottom_right.x, bottom_right.y)
    
    def is_point_visible(self, world_x: float, world_y: float) -> bool:
        """
        Check if a world point is visible in the current viewport.
        
        Args:
            world_x, world_y: World coordinates to check
            
        Returns:
            True if the point is visible
        """
        left, top, right, bottom = self.get_viewport_bounds()
        return left <= world_x <= right and top <= world_y <= bottom
    
    def is_circle_visible(self, world_x: float, world_y: float, radius: float) -> bool:
        """
        Check if a circle is visible in the current viewport.
        
        Args:
            world_x, world_y: Center of the circle in world coordinates
            radius: Radius of the circle in world units
            
        Returns:
            True if any part of the circle is visible
        """
        left, top, right, bottom = self.get_viewport_bounds()
        
        # Expand bounds by radius
        return (left - radius <= world_x <= right + radius and
                top - radius <= world_y <= bottom + radius)
    
    def get_visible_chunks(self, chunk_size: int) -> List[Tuple[int, int]]:
        """
        Get a list of chunk coordinates that are visible in the viewport.
        
        Args:
            chunk_size: Size of each chunk in world units
            
        Returns:
            List of (chunk_x, chunk_y) tuples
        """
        left, top, right, bottom = self.get_viewport_bounds()
        
        # Calculate chunk bounds
        min_chunk_x = int(math.floor(left / chunk_size))
        max_chunk_x = int(math.ceil(right / chunk_size))
        min_chunk_y = int(math.floor(top / chunk_size))
        max_chunk_y = int(math.ceil(bottom / chunk_size))
        
        chunks = []
        for chunk_x in range(min_chunk_x, max_chunk_x + 1):
            for chunk_y in range(min_chunk_y, max_chunk_y + 1):
                chunks.append((chunk_x, chunk_y))
        
        return chunks
    
    def focus_on_point(self, world_x: float, world_y: float, 
                      transition_speed: float = 5.0) -> None:
        """
        Smoothly move the camera to focus on a specific world point.
        
        Args:
            world_x, world_y: World coordinates to focus on
            transition_speed: Speed of the transition
        """
        target_position = Vector2(world_x, world_y)
        direction = target_position - self.position
        self.velocity += direction * transition_speed
    
    def serialize(self) -> Dict[str, Any]:
        """Serialize camera state for saving."""
        return {
            "position": {"x": self.position.x, "y": self.position.y},
            "zoom": self.zoom,
            "target_zoom": self.target_zoom
        }
    
    def deserialize(self, data: Dict[str, Any]) -> None:
        """Restore camera state from saved data."""
        self.position = Vector2(data["position"]["x"], data["position"]["y"])
        self.zoom = data["zoom"]
        self.target_zoom = data["target_zoom"]
        self.velocity = Vector2(0, 0)
