"""
Procedural world generation system.
"""

from typing import Dict, List, Tuple, Any
import logging

from ..game.config import WorldConfig
from ..utils.noise import NoiseGenerator
from .chunk import Chunk


class WorldGenerator:
    """
    Generates infinite procedural world using chunk-based system.
    """
    
    def __init__(self, config: WorldConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Noise generator for terrain
        self.noise = NoiseGenerator(config.biome_scale)
        
        # Generated chunks
        self.chunks: Dict[Tuple[int, int], Chunk] = {}
        self.world = self  # Placeholder for world data access
        
        self.logger.info("World generator initialized")
    
    def update(self, visible_chunks: List[Tuple[int, int]]) -> None:
        """Update world generation based on visible chunks."""
        for chunk_coord in visible_chunks:
            if chunk_coord not in self.chunks:
                self._generate_chunk(chunk_coord)
    
    def _generate_chunk(self, chunk_coord: Tuple[int, int]) -> None:
        """Generate a new chunk at the given coordinates."""
        chunk_x, chunk_y = chunk_coord
        
        # Create new chunk
        chunk = Chunk(chunk_x, chunk_y, self.config.chunk_size)
        
        # Generate terrain for this chunk
        self._generate_terrain(chunk)
        
        # Store chunk
        self.chunks[chunk_coord] = chunk
        
        self.logger.debug(f"Generated chunk at {chunk_coord}")
    
    def _generate_terrain(self, chunk: Chunk) -> None:
        """Generate terrain for a chunk."""
        # Placeholder terrain generation
        # In full implementation, this would use noise functions
        # to create varied terrain types
        
        for x in range(chunk.size):
            for y in range(chunk.size):
                world_x = chunk.world_x + x
                world_y = chunk.world_y + y
                
                # Simple noise-based terrain
                height = self.noise.perlin_2d(world_x, world_y, scale=0.01)
                
                if height < -0.3:
                    terrain_type = "water"
                elif height < 0.0:
                    terrain_type = "grass"
                elif height < 0.3:
                    terrain_type = "forest"
                else:
                    terrain_type = "mountain"
                
                chunk.set_terrain(x, y, terrain_type)
    
    def get_chunk(self, chunk_x: int, chunk_y: int) -> Chunk:
        """Get a chunk, generating it if necessary."""
        chunk_coord = (chunk_x, chunk_y)
        if chunk_coord not in self.chunks:
            self._generate_chunk(chunk_coord)
        return self.chunks[chunk_coord]
    
    def get_terrain_at(self, world_x: float, world_y: float) -> str:
        """Get terrain type at world coordinates."""
        chunk_x = int(world_x // self.config.chunk_size)
        chunk_y = int(world_y // self.config.chunk_size)
        
        local_x = int(world_x % self.config.chunk_size)
        local_y = int(world_y % self.config.chunk_size)
        
        chunk = self.get_chunk(chunk_x, chunk_y)
        return chunk.get_terrain(local_x, local_y)
    
    def serialize(self) -> Dict[str, Any]:
        """Serialize world state."""
        return {
            "chunks": {
                f"{x},{y}": chunk.serialize()
                for (x, y), chunk in self.chunks.items()
            }
        }
    
    def deserialize(self, data: Dict[str, Any]) -> None:
        """Restore world from saved data."""
        self.chunks.clear()
        
        for coord_str, chunk_data in data["chunks"].items():
            x, y = map(int, coord_str.split(","))
            chunk = Chunk.deserialize(chunk_data)
            self.chunks[(x, y)] = chunk
