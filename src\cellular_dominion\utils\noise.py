"""
Noise generation utilities for procedural content.
"""

import random
import math
from typing import Optional
from perlin_noise import PerlinNoise


class NoiseGenerator:
    """
    Wrapper for noise generation with various algorithms.

    Provides convenient methods for generating different types of noise
    commonly used in procedural generation.
    """

    def __init__(self, seed: Optional[int] = None):
        self.seed = seed or random.randint(0, 1000000)
        random.seed(self.seed)

    def perlin_2d(self, x: float, y: float, octaves: int = 4,
                  persistence: float = 0.5, lacunarity: float = 2.0,
                  scale: float = 1.0) -> float:
        """
        Generate 2D Perlin noise.

        Args:
            x, y: Coordinates
            octaves: Number of noise layers
            persistence: Amplitude multiplier for each octave
            lacunarity: Frequency multiplier for each octave
            scale: Overall scale of the noise

        Returns:
            Noise value between -1 and 1
        """
        return pnoise2(
            x * scale, y * scale,
            octaves=octaves,
            persistence=persistence,
            lacunarity=lacunarity,
            base=self.seed
        )

    def perlin_3d(self, x: float, y: float, z: float, octaves: int = 4,
                  persistence: float = 0.5, lacunarity: float = 2.0,
                  scale: float = 1.0) -> float:
        """
        Generate 3D Perlin noise.

        Useful for time-varying noise or volumetric effects.
        """
        return pnoise3(
            x * scale, y * scale, z * scale,
            octaves=octaves,
            persistence=persistence,
            lacunarity=lacunarity,
            base=self.seed
        )

    def ridged_noise(self, x: float, y: float, octaves: int = 4,
                     scale: float = 1.0) -> float:
        """
        Generate ridged noise (good for mountain ridges).
        """
        noise_val = self.perlin_2d(x, y, octaves, scale=scale)
        return 1.0 - abs(noise_val)

    def turbulence(self, x: float, y: float, octaves: int = 4,
                   scale: float = 1.0) -> float:
        """
        Generate turbulence noise (absolute value of Perlin noise).
        """
        return abs(self.perlin_2d(x, y, octaves, scale=scale))

    def fractal_brownian_motion(self, x: float, y: float, octaves: int = 4,
                                persistence: float = 0.5, lacunarity: float = 2.0,
                                scale: float = 1.0) -> float:
        """
        Generate fractal Brownian motion (fBm).

        This is essentially multi-octave Perlin noise with specific parameters.
        """
        return self.perlin_2d(x, y, octaves, persistence, lacunarity, scale)

    def domain_warping(self, x: float, y: float, warp_strength: float = 1.0,
                       scale: float = 1.0) -> float:
        """
        Apply domain warping to create more organic-looking noise.
        """
        # Generate offset values
        offset_x = self.perlin_2d(x, y, scale=scale * 0.1) * warp_strength
        offset_y = self.perlin_2d(x + 100, y + 100, scale=scale * 0.1) * warp_strength

        # Sample noise at warped coordinates
        return self.perlin_2d(x + offset_x, y + offset_y, scale=scale)

    def cellular_automata(self, x: int, y: int, width: int, height: int,
                         initial_density: float = 0.45, iterations: int = 5) -> bool:
        """
        Generate cellular automata pattern (good for cave systems).

        Returns True if the cell should be solid, False if empty.
        """
        # This is a simplified version - full implementation would maintain state
        # For now, just return a noise-based approximation
        noise_val = self.perlin_2d(x / width, y / height, scale=10.0)
        return noise_val > (initial_density * 2 - 1)

    def voronoi_noise(self, x: float, y: float, scale: float = 1.0,
                     num_points: int = 10) -> float:
        """
        Generate Voronoi noise pattern.

        Creates cellular/organic patterns good for biome boundaries.
        """
        # Simplified Voronoi - in full implementation would use proper algorithm
        min_distance = float('inf')

        # Generate pseudo-random points based on grid position
        grid_x = int(x * scale)
        grid_y = int(y * scale)

        for i in range(-1, 2):
            for j in range(-1, 2):
                # Generate point in neighboring cell
                cell_x = grid_x + i
                cell_y = grid_y + j

                # Pseudo-random point within cell
                random.seed(self.seed + cell_x * 374761393 + cell_y * 668265263)
                point_x = cell_x + random.random()
                point_y = cell_y + random.random()

                # Calculate distance
                dx = x * scale - point_x
                dy = y * scale - point_y
                distance = math.sqrt(dx * dx + dy * dy)

                min_distance = min(min_distance, distance)

        return min_distance

    def simplex_noise_2d(self, x: float, y: float, scale: float = 1.0) -> float:
        """
        Generate 2D simplex noise (alternative to Perlin with better properties).

        Note: This is a simplified implementation. For production use,
        consider using a proper simplex noise library.
        """
        # For now, use Perlin as approximation
        return self.perlin_2d(x, y, scale=scale)

    def get_biome_noise(self, x: float, y: float) -> tuple[float, float]:
        """
        Generate noise values suitable for biome determination.

        Returns:
            (temperature, humidity) values between 0 and 1
        """
        temperature = (self.perlin_2d(x, y, scale=0.01, octaves=3) + 1) / 2
        humidity = (self.perlin_2d(x + 1000, y + 1000, scale=0.01, octaves=3) + 1) / 2

        return temperature, humidity

    def get_resource_density(self, x: float, y: float, resource_type: str) -> float:
        """
        Generate resource density for a specific resource type.

        Returns value between 0 and 1 indicating resource abundance.
        """
        # Different resources use different noise patterns
        type_offset = hash(resource_type) % 10000

        density = self.perlin_2d(
            x + type_offset, y + type_offset,
            scale=0.005, octaves=4, persistence=0.6
        )

        # Convert to 0-1 range and apply threshold
        density = (density + 1) / 2
        return max(0, density - 0.3) / 0.7  # Threshold at 0.3, scale to 0-1
