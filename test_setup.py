#!/usr/bin/env python3
"""
Quick test script to verify the project setup works correctly.
"""

import sys
import traceback

def test_imports():
    """Test that all main modules can be imported."""
    print("Testing imports...")
    
    try:
        # Test core imports
        from cellular_dominion.game.config import GameConfig
        from cellular_dominion.entities.cell import Cell, CellTraits
        from cellular_dominion.utils.math_utils import Vector2
        from cellular_dominion.world.generator import WorldGenerator
        print("✓ Core imports successful")
        
        # Test configuration
        config = GameConfig()
        print("✓ Configuration system working")
        
        # Test cell creation
        position = Vector2(10, 20)
        traits = CellTraits()
        cell = Cell(position, traits)
        print("✓ Cell creation working")
        
        # Test world generation
        world_gen = WorldGenerator(config.world)
        chunk = world_gen.get_chunk(0, 0)
        print("✓ World generation working")
        
        print("\n🎉 All basic systems are working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        traceback.print_exc()
        return False

def test_dependencies():
    """Test that all required dependencies are available."""
    print("\nTesting dependencies...")
    
    required_packages = [
        'pygame',
        'numpy',
        'noise',
        'pydantic',
        'click'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install them with: pip install -e .")
        return False
    else:
        print("\n✓ All dependencies are available!")
        return True

def main():
    """Run all tests."""
    print("Cellular Dominion - Setup Test")
    print("=" * 40)
    
    deps_ok = test_dependencies()
    imports_ok = test_imports()
    
    if deps_ok and imports_ok:
        print("\n🚀 Project setup is complete and working!")
        print("\nNext steps:")
        print("1. Install the project: pip install -e .")
        print("2. Run the game: cellular-dominion")
        print("3. Or run directly: python -m cellular_dominion.main")
        print("4. Run tests: pytest")
        return 0
    else:
        print("\n❌ Setup issues detected. Please fix the above errors.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
