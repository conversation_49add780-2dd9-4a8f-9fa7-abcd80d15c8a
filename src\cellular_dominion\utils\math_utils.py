"""
Mathematical utility functions and classes.
"""

import math
from typing import <PERSON>ple, Union


class Vector2:
    """2D vector class for position and velocity calculations."""
    
    def __init__(self, x: float = 0.0, y: float = 0.0):
        self.x = x
        self.y = y
    
    def __add__(self, other: 'Vector2') -> 'Vector2':
        return Vector2(self.x + other.x, self.y + other.y)
    
    def __sub__(self, other: 'Vector2') -> 'Vector2':
        return Vector2(self.x - other.x, self.y - other.y)
    
    def __mul__(self, scalar: float) -> 'Vector2':
        return Vector2(self.x * scalar, self.y * scalar)
    
    def __rmul__(self, scalar: float) -> 'Vector2':
        return self.__mul__(scalar)
    
    def __truediv__(self, scalar: float) -> 'Vector2':
        return Vector2(self.x / scalar, self.y / scalar)
    
    def __eq__(self, other: 'Vector2') -> bool:
        return abs(self.x - other.x) < 1e-6 and abs(self.y - other.y) < 1e-6
    
    def __repr__(self) -> str:
        return f"Vector2({self.x:.2f}, {self.y:.2f})"
    
    def magnitude(self) -> float:
        """Calculate the magnitude (length) of the vector."""
        return math.sqrt(self.x * self.x + self.y * self.y)
    
    def magnitude_squared(self) -> float:
        """Calculate the squared magnitude (faster than magnitude)."""
        return self.x * self.x + self.y * self.y
    
    def normalized(self) -> 'Vector2':
        """Return a normalized (unit length) version of this vector."""
        mag = self.magnitude()
        if mag == 0:
            return Vector2(0, 0)
        return Vector2(self.x / mag, self.y / mag)
    
    def normalize(self) -> None:
        """Normalize this vector in place."""
        mag = self.magnitude()
        if mag != 0:
            self.x /= mag
            self.y /= mag
    
    def dot(self, other: 'Vector2') -> float:
        """Calculate the dot product with another vector."""
        return self.x * other.x + self.y * other.y
    
    def cross(self, other: 'Vector2') -> float:
        """Calculate the 2D cross product (returns scalar)."""
        return self.x * other.y - self.y * other.x
    
    def angle(self) -> float:
        """Get the angle of this vector in radians."""
        return math.atan2(self.y, self.x)
    
    def rotate(self, angle: float) -> 'Vector2':
        """Return a rotated version of this vector."""
        cos_a = math.cos(angle)
        sin_a = math.sin(angle)
        return Vector2(
            self.x * cos_a - self.y * sin_a,
            self.x * sin_a + self.y * cos_a
        )
    
    def lerp(self, other: 'Vector2', t: float) -> 'Vector2':
        """Linear interpolation between this vector and another."""
        return Vector2(
            self.x + (other.x - self.x) * t,
            self.y + (other.y - self.y) * t
        )
    
    def to_tuple(self) -> Tuple[float, float]:
        """Convert to tuple."""
        return (self.x, self.y)
    
    def to_int_tuple(self) -> Tuple[int, int]:
        """Convert to integer tuple."""
        return (int(self.x), int(self.y))
    
    @classmethod
    def from_angle(cls, angle: float, magnitude: float = 1.0) -> 'Vector2':
        """Create a vector from an angle and magnitude."""
        return cls(math.cos(angle) * magnitude, math.sin(angle) * magnitude)
    
    @classmethod
    def zero(cls) -> 'Vector2':
        """Create a zero vector."""
        return cls(0, 0)
    
    @classmethod
    def one(cls) -> 'Vector2':
        """Create a vector with both components set to 1."""
        return cls(1, 1)
    
    @classmethod
    def up(cls) -> 'Vector2':
        """Create an up vector (0, 1)."""
        return cls(0, 1)
    
    @classmethod
    def down(cls) -> 'Vector2':
        """Create a down vector (0, -1)."""
        return cls(0, -1)
    
    @classmethod
    def left(cls) -> 'Vector2':
        """Create a left vector (-1, 0)."""
        return cls(-1, 0)
    
    @classmethod
    def right(cls) -> 'Vector2':
        """Create a right vector (1, 0)."""
        return cls(1, 0)


def distance(a: Vector2, b: Vector2) -> float:
    """Calculate the distance between two points."""
    return (b - a).magnitude()


def distance_squared(a: Vector2, b: Vector2) -> float:
    """Calculate the squared distance between two points (faster)."""
    return (b - a).magnitude_squared()


def clamp(value: float, min_val: float, max_val: float) -> float:
    """Clamp a value between min and max."""
    return max(min_val, min(max_val, value))


def lerp(a: float, b: float, t: float) -> float:
    """Linear interpolation between two values."""
    return a + (b - a) * t


def smoothstep(edge0: float, edge1: float, x: float) -> float:
    """Smooth interpolation function."""
    t = clamp((x - edge0) / (edge1 - edge0), 0.0, 1.0)
    return t * t * (3.0 - 2.0 * t)


def wrap_angle(angle: float) -> float:
    """Wrap an angle to the range [-π, π]."""
    while angle > math.pi:
        angle -= 2 * math.pi
    while angle < -math.pi:
        angle += 2 * math.pi
    return angle


def angle_difference(a: float, b: float) -> float:
    """Calculate the shortest angular difference between two angles."""
    diff = b - a
    return wrap_angle(diff)


def map_range(value: float, in_min: float, in_max: float, out_min: float, out_max: float) -> float:
    """Map a value from one range to another."""
    return out_min + (out_max - out_min) * ((value - in_min) / (in_max - in_min))


def sign(value: float) -> int:
    """Return the sign of a value (-1, 0, or 1)."""
    if value > 0:
        return 1
    elif value < 0:
        return -1
    else:
        return 0
