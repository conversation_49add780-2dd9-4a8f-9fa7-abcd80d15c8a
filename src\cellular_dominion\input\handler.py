"""
Input handling system for keyboard and mouse events.
"""

import pygame
from typing import List, Dict, Any, Set
from enum import Enum


class InputHandler:
    """
    Handles keyboard and mouse input, converting them to game commands.
    """
    
    def __init__(self):
        self.commands: List[Dict[str, Any]] = []
        self.keys_pressed: Set[int] = set()
        self.mouse_pos = (0, 0)
        self.mouse_buttons = [False, False, False]  # Left, Middle, Right
        
        # Camera movement speed
        self.camera_speed = 200.0
    
    def handle_event(self, event: pygame.event.Event) -> None:
        """Process a pygame event."""
        if event.type == pygame.KEYDOWN:
            self._handle_keydown(event)
            self.keys_pressed.add(event.key)
        
        elif event.type == pygame.KEYUP:
            self._handle_keyup(event)
            self.keys_pressed.discard(event.key)
        
        elif event.type == pygame.MOUSEBUTTONDOWN:
            self._handle_mouse_down(event)
        
        elif event.type == pygame.MOUSEBUTTONUP:
            self._handle_mouse_up(event)
        
        elif event.type == pygame.MOUSEMOTION:
            self.mouse_pos = event.pos
        
        elif event.type == pygame.MOUSEWHEEL:
            self._handle_mouse_wheel(event)
    
    def _handle_keydown(self, event: pygame.event.Event) -> None:
        """Handle key press events."""
        key = event.key
        
        if key == pygame.K_ESCAPE:
            self.commands.append({"type": "quit"})
        
        elif key == pygame.K_SPACE:
            self.commands.append({"type": "toggle_pause"})
        
        elif key == pygame.K_s and (event.mod & pygame.KMOD_CTRL):
            self.commands.append({"type": "save_game"})
        
        elif key == pygame.K_l and (event.mod & pygame.KMOD_CTRL):
            self.commands.append({"type": "load_game"})
    
    def _handle_keyup(self, event: pygame.event.Event) -> None:
        """Handle key release events."""
        pass
    
    def _handle_mouse_down(self, event: pygame.event.Event) -> None:
        """Handle mouse button press events."""
        if event.button < len(self.mouse_buttons):
            self.mouse_buttons[event.button - 1] = True
    
    def _handle_mouse_up(self, event: pygame.event.Event) -> None:
        """Handle mouse button release events."""
        if event.button < len(self.mouse_buttons):
            self.mouse_buttons[event.button - 1] = False
    
    def _handle_mouse_wheel(self, event: pygame.event.Event) -> None:
        """Handle mouse wheel events."""
        zoom_factor = 1.1 if event.y > 0 else 0.9
        self.commands.append({
            "type": "camera_zoom",
            "factor": zoom_factor
        })
    
    def update(self, delta_time: float) -> None:
        """Update input state and generate continuous commands."""
        # Handle continuous key presses for camera movement
        dx, dy = 0, 0
        
        if pygame.K_w in self.keys_pressed or pygame.K_UP in self.keys_pressed:
            dy -= self.camera_speed * delta_time
        if pygame.K_s in self.keys_pressed or pygame.K_DOWN in self.keys_pressed:
            dy += self.camera_speed * delta_time
        if pygame.K_a in self.keys_pressed or pygame.K_LEFT in self.keys_pressed:
            dx -= self.camera_speed * delta_time
        if pygame.K_d in self.keys_pressed or pygame.K_RIGHT in self.keys_pressed:
            dx += self.camera_speed * delta_time
        
        if dx != 0 or dy != 0:
            self.commands.append({
                "type": "camera_move",
                "dx": dx,
                "dy": dy
            })
    
    def get_commands(self) -> List[Dict[str, Any]]:
        """Get and clear the command queue."""
        commands = self.commands.copy()
        self.commands.clear()
        return commands
    
    def is_key_pressed(self, key: int) -> bool:
        """Check if a specific key is currently pressed."""
        return key in self.keys_pressed
    
    def get_mouse_position(self) -> tuple[int, int]:
        """Get current mouse position."""
        return self.mouse_pos
    
    def is_mouse_button_pressed(self, button: int) -> bool:
        """Check if a mouse button is currently pressed (0=left, 1=middle, 2=right)."""
        if 0 <= button < len(self.mouse_buttons):
            return self.mouse_buttons[button]
        return False
