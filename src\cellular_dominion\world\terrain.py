"""
Terrain types and properties.
"""

from enum import Enum
from typing import Dict, Any
from dataclasses import dataclass


class TerrainType(Enum):
    """Different terrain types in the world."""
    WATER = "water"
    GRASS = "grass"
    FOREST = "forest"
    MOUNTAIN = "mountain"
    DESERT = "desert"
    SNOW = "snow"


@dataclass
class TerrainProperties:
    """Properties of a terrain type."""
    movement_cost: float
    resource_multiplier: Dict[str, float]
    color: tuple[int, int, int]
    description: str


class TerrainManager:
    """
    Manages terrain types and their properties.
    """
    
    def __init__(self):
        self.terrain_properties = {
            TerrainType.WATER: TerrainProperties(
                movement_cost=2.0,
                resource_multiplier={"food": 0.5, "wood": 0.0},
                color=(64, 128, 255),
                description="Bodies of water"
            ),
            TerrainType.GRASS: TerrainProperties(
                movement_cost=1.0,
                resource_multiplier={"food": 1.0, "wood": 0.2},
                color=(34, 139, 34),
                description="Open grasslands"
            ),
            TerrainType.FOREST: TerrainProperties(
                movement_cost=1.5,
                resource_multiplier={"food": 0.8, "wood": 2.0},
                color=(0, 100, 0),
                description="Dense forests"
            ),
            TerrainType.MOUNTAIN: TerrainProperties(
                movement_cost=3.0,
                resource_multiplier={"stone": 2.0, "metal": 1.5},
                color=(139, 69, 19),
                description="Rocky mountains"
            ),
            TerrainType.DESERT: TerrainProperties(
                movement_cost=1.8,
                resource_multiplier={"food": 0.2, "energy": 1.5},
                color=(238, 203, 173),
                description="Arid desert"
            ),
            TerrainType.SNOW: TerrainProperties(
                movement_cost=2.5,
                resource_multiplier={"food": 0.1, "water": 1.0},
                color=(255, 250, 250),
                description="Frozen tundra"
            )
        }
    
    def get_properties(self, terrain_type: TerrainType) -> TerrainProperties:
        """Get properties for a terrain type."""
        return self.terrain_properties[terrain_type]
    
    def get_movement_cost(self, terrain_type: TerrainType) -> float:
        """Get movement cost for terrain."""
        return self.terrain_properties[terrain_type].movement_cost
    
    def get_resource_multiplier(self, terrain_type: TerrainType, resource: str) -> float:
        """Get resource multiplier for terrain and resource type."""
        props = self.terrain_properties[terrain_type]
        return props.resource_multiplier.get(resource, 1.0)
