"""
Civilization management system.
"""

from typing import List, Dict, Any
import logging

from .cell import Cell, CellTraits
from ..utils.math_utils import Vector2
from ..game.config import CellConfig


class Civilization:
    """
    Manages a civilization composed of autonomous cells.
    
    Handles cell population, resource management, and civilization-level behaviors.
    """
    
    def __init__(self, config: CellConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Cell population
        self.cells: List[Cell] = []
        self.max_cells = config.max_energy  # Using max_energy as placeholder
        
        # Civilization stats
        self.total_energy = 0.0
        self.population = 0
        self.age = 0.0
        
        # Culture and technology (placeholders)
        self.culture_traits: Dict[str, float] = {}
        self.technologies: List[str] = []
        
        # Initialize with starting cells
        self._create_initial_population()
        
        self.logger.info(f"Civilization initialized with {len(self.cells)} cells")
    
    def _create_initial_population(self) -> None:
        """Create the initial cell population."""
        initial_count = 10  # Could be from config
        
        for i in range(initial_count):
            # Random position near origin
            import random
            x = random.uniform(-50, 50)
            y = random.uniform(-50, 50)
            position = Vector2(x, y)
            
            # Create cell with random traits
            traits = CellTraits()
            cell = Cell(position, traits)
            self.cells.append(cell)
    
    def update(self, delta_time: float, world_data: Any) -> None:
        """Update the civilization."""
        self.age += delta_time
        
        # Update all cells
        new_cells = []
        cells_to_remove = []
        
        for cell in self.cells:
            # Update cell
            offspring = cell.update(delta_time, world_data)
            
            # Handle reproduction
            if offspring and len(self.cells) + len(new_cells) < self.max_cells:
                new_cells.append(offspring)
            
            # Remove dead cells
            if cell.energy <= 0:
                cells_to_remove.append(cell)
        
        # Add new cells
        self.cells.extend(new_cells)
        
        # Remove dead cells
        for cell in cells_to_remove:
            self.cells.remove(cell)
        
        # Update civilization stats
        self._update_stats()
    
    def _update_stats(self) -> None:
        """Update civilization-level statistics."""
        self.population = len(self.cells)
        self.total_energy = sum(cell.energy for cell in self.cells)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get civilization statistics."""
        return {
            "population": self.population,
            "total_energy": self.total_energy,
            "age": self.age,
            "technologies": len(self.technologies),
            "culture_traits": self.culture_traits.copy()
        }
    
    def serialize(self) -> Dict[str, Any]:
        """Serialize civilization data."""
        return {
            "cells": [cell.serialize() for cell in self.cells],
            "age": self.age,
            "culture_traits": self.culture_traits,
            "technologies": self.technologies
        }
    
    @classmethod
    def deserialize(cls, data: Dict[str, Any], config: CellConfig) -> 'Civilization':
        """Create civilization from serialized data."""
        civilization = cls(config)
        civilization.cells = [Cell.deserialize(cell_data) for cell_data in data["cells"]]
        civilization.age = data["age"]
        civilization.culture_traits = data["culture_traits"]
        civilization.technologies = data["technologies"]
        civilization._update_stats()
        return civilization
