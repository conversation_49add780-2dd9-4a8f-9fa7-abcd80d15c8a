"""
Main rendering system using Pygame.
"""

import pygame
from typing import Any, List
import logging

from ..game.config import GameConfig
from .camera import Camera
from .colors import ColorScheme


class Renderer:
    """
    Main rendering system for Cellular Dominion.
    
    Handles all drawing operations using Pygame.
    """
    
    def __init__(self, config: GameConfig, camera: Camera):
        self.config = config
        self.camera = camera
        self.logger = logging.getLogger(__name__)
        
        # Initialize display
        flags = pygame.DOUBLEBUF
        if config.display.fullscreen:
            flags |= pygame.FULLSCREEN
        
        self.screen = pygame.display.set_mode(
            (config.display.width, config.display.height),
            flags
        )
        pygame.display.set_caption("Cellular Dominion")
        
        # Enable VSync if requested
        if config.display.vsync:
            pygame.display.flip()
        
        # Font for UI text
        pygame.font.init()
        self.font = pygame.font.Font(None, 24)
        self.small_font = pygame.font.Font(None, 16)
        
        self.logger.info("Renderer initialized")
    
    def begin_frame(self) -> None:
        """Begin rendering a new frame."""
        self.screen.fill(ColorScheme.BLACK)
    
    def end_frame(self) -> None:
        """Complete the frame and display it."""
        pygame.display.flip()
    
    def render_world(self, world_data: Any) -> None:
        """
        Render the game world.
        
        Args:
            world_data: World data to render (placeholder for now)
        """
        # Placeholder: Draw a simple grid
        viewport_bounds = self.camera.get_viewport_bounds()
        left, top, right, bottom = viewport_bounds
        
        # Draw grid lines
        grid_size = 50
        
        # Vertical lines
        start_x = int(left // grid_size) * grid_size
        for x in range(int(start_x), int(right) + grid_size, grid_size):
            screen_start = self.camera.world_to_screen(x, top)
            screen_end = self.camera.world_to_screen(x, bottom)
            if 0 <= screen_start[0] <= self.config.display.width:
                pygame.draw.line(self.screen, ColorScheme.DARK_GRAY, 
                               screen_start, screen_end, 1)
        
        # Horizontal lines
        start_y = int(top // grid_size) * grid_size
        for y in range(int(start_y), int(bottom) + grid_size, grid_size):
            screen_start = self.camera.world_to_screen(left, y)
            screen_end = self.camera.world_to_screen(right, y)
            if 0 <= screen_start[1] <= self.config.display.height:
                pygame.draw.line(self.screen, ColorScheme.DARK_GRAY, 
                               screen_start, screen_end, 1)
    
    def render_civilization(self, civilization: Any) -> None:
        """
        Render the civilization (cells, buildings, etc.).
        
        Args:
            civilization: Civilization data to render
        """
        # Placeholder: This would render actual cells
        # For now, just draw some sample cells
        if hasattr(civilization, 'cells'):
            for cell in civilization.cells:
                if self.camera.is_point_visible(cell.position.x, cell.position.y):
                    screen_pos = self.camera.world_to_screen(cell.position.x, cell.position.y)
                    color = cell.get_color()
                    radius = max(2, int(5 * self.camera.zoom))
                    pygame.draw.circle(self.screen, color, screen_pos, radius)
    
    def render_ui(self, state_manager: Any, civilization: Any) -> None:
        """
        Render the user interface.
        
        Args:
            state_manager: Game state manager
            civilization: Civilization for stats display
        """
        # Render basic UI elements
        self._render_info_panel(state_manager, civilization)
        self._render_controls_help()
    
    def _render_info_panel(self, state_manager: Any, civilization: Any) -> None:
        """Render information panel."""
        panel_width = 200
        panel_height = 150
        panel_x = 10
        panel_y = 10
        
        # Background
        panel_rect = pygame.Rect(panel_x, panel_y, panel_width, panel_height)
        pygame.draw.rect(self.screen, ColorScheme.UI_BACKGROUND, panel_rect)
        pygame.draw.rect(self.screen, ColorScheme.UI_BORDER, panel_rect, 2)
        
        # Text content
        y_offset = panel_y + 10
        line_height = 20
        
        # Game state
        state_text = f"State: {state_manager.get_current_state().value if state_manager else 'Unknown'}"
        text_surface = self.font.render(state_text, True, ColorScheme.UI_TEXT)
        self.screen.blit(text_surface, (panel_x + 10, y_offset))
        y_offset += line_height
        
        # Camera info
        cam_text = f"Zoom: {self.camera.zoom:.2f}"
        text_surface = self.font.render(cam_text, True, ColorScheme.UI_TEXT)
        self.screen.blit(text_surface, (panel_x + 10, y_offset))
        y_offset += line_height
        
        # Civilization info (placeholder)
        cell_count = len(civilization.cells) if hasattr(civilization, 'cells') else 0
        cell_text = f"Cells: {cell_count}"
        text_surface = self.font.render(cell_text, True, ColorScheme.UI_TEXT)
        self.screen.blit(text_surface, (panel_x + 10, y_offset))
    
    def _render_controls_help(self) -> None:
        """Render controls help."""
        help_text = [
            "Controls:",
            "WASD - Move camera",
            "Mouse wheel - Zoom",
            "Space - Pause",
            "ESC - Quit"
        ]
        
        x = self.config.display.width - 150
        y = 10
        line_height = 18
        
        for line in help_text:
            text_surface = self.small_font.render(line, True, ColorScheme.UI_TEXT)
            self.screen.blit(text_surface, (x, y))
            y += line_height
    
    def render_fps(self, fps: float) -> None:
        """Render FPS counter."""
        fps_text = f"FPS: {fps:.1f}"
        text_surface = self.font.render(fps_text, True, ColorScheme.UI_TEXT)
        
        # Position in bottom-right corner
        text_rect = text_surface.get_rect()
        text_rect.bottomright = (self.config.display.width - 10, 
                                self.config.display.height - 10)
        
        self.screen.blit(text_surface, text_rect)
    
    def render_debug_info(self, debug_data: dict) -> None:
        """Render debug information."""
        y = self.config.display.height // 2
        x = 10
        line_height = 18
        
        for key, value in debug_data.items():
            debug_text = f"{key}: {value}"
            text_surface = self.small_font.render(debug_text, True, ColorScheme.UI_TEXT)
            self.screen.blit(text_surface, (x, y))
            y += line_height
