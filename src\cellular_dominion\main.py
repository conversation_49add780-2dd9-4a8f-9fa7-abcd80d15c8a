"""
Main entry point for Cellular Dominion game.
"""

import sys
import logging
from pathlib import Path
import click

from .game.engine import GameEngine
from .game.config import GameConfig


def setup_logging(debug: bool = False) -> None:
    """Set up logging configuration."""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.FileHandler("cellular_dominion.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


@click.command()
@click.option("--debug", is_flag=True, help="Enable debug logging")
@click.option("--config", type=click.Path(exists=True), help="Path to config file")
@click.option("--fullscreen", is_flag=True, help="Start in fullscreen mode")
@click.option("--width", default=1280, help="Window width")
@click.option("--height", default=720, help="Window height")
def main(debug: bool, config: str, fullscreen: bool, width: int, height: int) -> None:
    """
    Launch Cellular Dominion game.
    
    An emergent civilization-building game where simple rules create infinite complexity.
    """
    setup_logging(debug)
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("Starting Cellular Dominion...")
        
        # Load configuration
        game_config = GameConfig()
        if config:
            game_config.load_from_file(Path(config))
        
        # Override config with command line arguments
        if fullscreen:
            game_config.display.fullscreen = True
        game_config.display.width = width
        game_config.display.height = height
        
        # Initialize and run the game
        game = GameEngine(game_config)
        game.run()
        
    except KeyboardInterrupt:
        logger.info("Game interrupted by user")
    except Exception as e:
        logger.error(f"Game crashed with error: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("Cellular Dominion shutting down")


if __name__ == "__main__":
    main()
