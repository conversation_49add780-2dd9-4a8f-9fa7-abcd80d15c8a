"""
Chunk-based world representation.
"""

from typing import Dict, Any, List
import numpy as np


class Chunk:
    """
    Represents a square chunk of the world.
    
    Contains terrain data, resources, and other world features.
    """
    
    def __init__(self, chunk_x: int, chunk_y: int, size: int):
        self.chunk_x = chunk_x
        self.chunk_y = chunk_y
        self.size = size
        
        # World coordinates of chunk origin
        self.world_x = chunk_x * size
        self.world_y = chunk_y * size
        
        # Terrain data (using strings for simplicity)
        self.terrain: List[List[str]] = [
            ["grass" for _ in range(size)] for _ in range(size)
        ]
        
        # Resource data (placeholder)
        self.resources: Dict[str, np.ndarray] = {}
        
        # Buildings and structures (placeholder)
        self.structures: List[Any] = []
    
    def set_terrain(self, local_x: int, local_y: int, terrain_type: str) -> None:
        """Set terrain type at local coordinates."""
        if 0 <= local_x < self.size and 0 <= local_y < self.size:
            self.terrain[local_y][local_x] = terrain_type
    
    def get_terrain(self, local_x: int, local_y: int) -> str:
        """Get terrain type at local coordinates."""
        if 0 <= local_x < self.size and 0 <= local_y < self.size:
            return self.terrain[local_y][local_x]
        return "grass"  # Default terrain
    
    def add_resource(self, resource_type: str, density_map: np.ndarray) -> None:
        """Add a resource density map to this chunk."""
        self.resources[resource_type] = density_map
    
    def get_resource_density(self, local_x: int, local_y: int, resource_type: str) -> float:
        """Get resource density at local coordinates."""
        if resource_type in self.resources:
            if 0 <= local_x < self.size and 0 <= local_y < self.size:
                return float(self.resources[resource_type][local_y, local_x])
        return 0.0
    
    def serialize(self) -> Dict[str, Any]:
        """Serialize chunk data."""
        return {
            "chunk_x": self.chunk_x,
            "chunk_y": self.chunk_y,
            "size": self.size,
            "terrain": self.terrain,
            "resources": {
                name: array.tolist() for name, array in self.resources.items()
            }
        }
    
    @classmethod
    def deserialize(cls, data: Dict[str, Any]) -> 'Chunk':
        """Create chunk from serialized data."""
        chunk = cls(data["chunk_x"], data["chunk_y"], data["size"])
        chunk.terrain = data["terrain"]
        
        # Restore resource arrays
        for name, array_data in data["resources"].items():
            chunk.resources[name] = np.array(array_data)
        
        return chunk
