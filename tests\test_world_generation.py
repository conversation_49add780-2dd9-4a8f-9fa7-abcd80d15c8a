"""
Tests for world generation system.
"""

import pytest
from cellular_dominion.world.generator import WorldGenerator
from cellular_dominion.world.chunk import Chunk
from cellular_dominion.game.config import WorldConfig


class TestWorldGenerator:
    """Test cases for WorldGenerator class."""
    
    def test_world_generator_creation(self):
        """Test basic world generator creation."""
        config = WorldConfig()
        generator = WorldGenerator(config)
        
        assert generator.config == config
        assert len(generator.chunks) == 0
    
    def test_chunk_generation(self):
        """Test chunk generation."""
        config = WorldConfig(chunk_size=32)
        generator = WorldGenerator(config)
        
        # Request a chunk
        chunk = generator.get_chunk(0, 0)
        
        assert chunk is not None
        assert chunk.chunk_x == 0
        assert chunk.chunk_y == 0
        assert chunk.size == 32
        assert (0, 0) in generator.chunks
    
    def test_multiple_chunk_generation(self):
        """Test generation of multiple chunks."""
        config = WorldConfig(chunk_size=16)
        generator = WorldGenerator(config)
        
        # Generate several chunks
        chunks = []
        for x in range(-1, 2):
            for y in range(-1, 2):
                chunk = generator.get_chunk(x, y)
                chunks.append(chunk)
        
        assert len(chunks) == 9
        assert len(generator.chunks) == 9
        
        # Check that all chunks are unique
        chunk_coords = [(c.chunk_x, c.chunk_y) for c in chunks]
        assert len(set(chunk_coords)) == 9
    
    def test_terrain_generation(self):
        """Test that terrain is generated for chunks."""
        config = WorldConfig(chunk_size=8)
        generator = WorldGenerator(config)
        
        chunk = generator.get_chunk(0, 0)
        
        # Check that terrain exists for all positions
        for x in range(chunk.size):
            for y in range(chunk.size):
                terrain = chunk.get_terrain(x, y)
                assert terrain is not None
                assert isinstance(terrain, str)
                assert terrain in ["water", "grass", "forest", "mountain"]
    
    def test_terrain_at_world_coordinates(self):
        """Test getting terrain at world coordinates."""
        config = WorldConfig(chunk_size=16)
        generator = WorldGenerator(config)
        
        # Test various world coordinates
        terrain1 = generator.get_terrain_at(0, 0)
        terrain2 = generator.get_terrain_at(15, 15)
        terrain3 = generator.get_terrain_at(-5, 10)
        
        assert terrain1 is not None
        assert terrain2 is not None
        assert terrain3 is not None


class TestChunk:
    """Test cases for Chunk class."""
    
    def test_chunk_creation(self):
        """Test basic chunk creation."""
        chunk = Chunk(2, 3, 16)
        
        assert chunk.chunk_x == 2
        assert chunk.chunk_y == 3
        assert chunk.size == 16
        assert chunk.world_x == 32  # 2 * 16
        assert chunk.world_y == 48  # 3 * 16
    
    def test_terrain_operations(self):
        """Test terrain setting and getting."""
        chunk = Chunk(0, 0, 8)
        
        # Set terrain
        chunk.set_terrain(3, 4, "forest")
        
        # Get terrain
        terrain = chunk.get_terrain(3, 4)
        assert terrain == "forest"
        
        # Test bounds
        terrain_out_of_bounds = chunk.get_terrain(10, 10)
        assert terrain_out_of_bounds == "grass"  # Default
    
    def test_chunk_serialization(self):
        """Test chunk serialization and deserialization."""
        original_chunk = Chunk(1, 2, 4)
        original_chunk.set_terrain(1, 1, "mountain")
        original_chunk.set_terrain(2, 3, "water")
        
        # Serialize
        data = original_chunk.serialize()
        
        # Deserialize
        restored_chunk = Chunk.deserialize(data)
        
        # Check that data is preserved
        assert restored_chunk.chunk_x == original_chunk.chunk_x
        assert restored_chunk.chunk_y == original_chunk.chunk_y
        assert restored_chunk.size == original_chunk.size
        assert restored_chunk.get_terrain(1, 1) == "mountain"
        assert restored_chunk.get_terrain(2, 3) == "water"


if __name__ == "__main__":
    pytest.main([__file__])
