"""
Tests for cell entities and behaviors.
"""

import pytest
from cellular_dominion.entities.cell import Cell, CellTraits, CellSpecialization
from cellular_dominion.utils.math_utils import Vector2


class TestCell:
    """Test cases for Cell class."""
    
    def test_cell_creation(self):
        """Test basic cell creation."""
        position = Vector2(10, 20)
        cell = Cell(position)
        
        assert cell.position == position
        assert cell.energy == 100.0
        assert cell.specialization == CellSpecialization.GENERALIST
        assert cell.age == 0.0
    
    def test_cell_with_traits(self):
        """Test cell creation with custom traits."""
        traits = CellTraits(energy_efficiency=1.5, movement_speed=2.0)
        position = Vector2(0, 0)
        cell = Cell(position, traits)
        
        assert cell.traits.energy_efficiency == 1.5
        assert cell.traits.movement_speed == 2.0
    
    def test_cell_update(self):
        """Test cell update mechanics."""
        cell = Cell(Vector2(0, 0))
        initial_age = cell.age
        initial_energy = cell.energy
        
        # Update cell
        cell.update(1.0, None)  # 1 second update
        
        # Age should increase
        assert cell.age > initial_age
        
        # Energy should decrease (due to consumption)
        assert cell.energy < initial_energy
    
    def test_cell_reproduction(self):
        """Test cell reproduction mechanics."""
        cell = Cell(Vector2(0, 0))
        cell.energy = 90.0  # High energy for reproduction
        cell.age = 15.0     # Old enough to reproduce
        
        # Force reproduction by setting high reproduction drive
        cell.traits.reproduction_drive = 10.0
        
        # Update multiple times to trigger reproduction
        offspring = None
        for _ in range(100):  # Try many times due to randomness
            result = cell.update(0.1, None)
            if result is not None:
                offspring = result
                break
        
        # Should eventually produce offspring
        assert offspring is not None
        assert isinstance(offspring, Cell)
        assert offspring.id != cell.id
    
    def test_cell_serialization(self):
        """Test cell serialization and deserialization."""
        original_cell = Cell(Vector2(5, 10))
        original_cell.energy = 75.0
        original_cell.age = 25.0
        original_cell.specialization = CellSpecialization.MINER
        
        # Serialize
        data = original_cell.serialize()
        
        # Deserialize
        restored_cell = Cell.deserialize(data)
        
        # Check that data is preserved
        assert restored_cell.position.x == original_cell.position.x
        assert restored_cell.position.y == original_cell.position.y
        assert restored_cell.energy == original_cell.energy
        assert restored_cell.age == original_cell.age
        assert restored_cell.specialization == original_cell.specialization


class TestCellTraits:
    """Test cases for CellTraits class."""
    
    def test_trait_mutation(self):
        """Test trait mutation mechanics."""
        original_traits = CellTraits(energy_efficiency=1.0, movement_speed=1.0)
        
        # Mutate with high mutation rate
        mutated_traits = original_traits.mutate(1.0)  # 100% mutation rate
        
        # Traits should be different (with very high probability)
        assert (mutated_traits.energy_efficiency != original_traits.energy_efficiency or
                mutated_traits.movement_speed != original_traits.movement_speed)
    
    def test_trait_mutation_bounds(self):
        """Test that mutated traits stay within reasonable bounds."""
        traits = CellTraits(energy_efficiency=1.0)
        
        # Mutate many times
        for _ in range(100):
            traits = traits.mutate(0.1)
            
            # Should never go below minimum
            assert traits.energy_efficiency >= 0.01
            
            # Should stay within reasonable range
            assert traits.energy_efficiency < 10.0


if __name__ == "__main__":
    pytest.main([__file__])
