"""
Color schemes and color utilities for the game.
"""

from typing import Tu<PERSON>, Dict
from enum import Enum


class ColorScheme:
    """Color scheme definitions for the game."""
    
    # Basic colors
    BLACK = (0, 0, 0)
    WHITE = (255, 255, 255)
    GRAY = (128, 128, 128)
    LIGHT_GRAY = (192, 192, 192)
    DARK_GRAY = (64, 64, 64)
    
    # Cell specialization colors (matching CellSpecialization)
    GENERALIST = (128, 128, 128)  # Gray
    MINER = (255, 0, 0)           # Red
    BUILDER = (0, 0, 255)         # Blue
    SCOUT = (0, 255, 0)           # Green
    RESEARCHER = (128, 0, 128)    # Purple
    TRADER = (255, 255, 0)        # Yellow
    
    # Terrain colors
    WATER = (64, 128, 255)
    GRASS = (34, 139, 34)
    FOREST = (0, 100, 0)
    MOUNTAIN = (139, 69, 19)
    DESERT = (238, 203, 173)
    SNOW = (255, 250, 250)
    
    # UI colors
    UI_BACKGROUND = (32, 32, 32)
    UI_BORDER = (64, 64, 64)
    UI_TEXT = (255, 255, 255)
    UI_HIGHLIGHT = (100, 149, 237)
    UI_BUTTON = (70, 70, 70)
    UI_BUTTON_HOVER = (90, 90, 90)
    
    # Resource colors
    FOOD = (255, 165, 0)      # Orange
    WOOD = (139, 69, 19)      # Brown
    STONE = (128, 128, 128)   # Gray
    METAL = (192, 192, 192)   # Silver
    ENERGY = (255, 255, 0)    # Yellow
    
    @classmethod
    def get_cell_color(cls, specialization_name: str) -> Tuple[int, int, int]:
        """Get color for a cell specialization."""
        color_map = {
            "GENERALIST": cls.GENERALIST,
            "MINER": cls.MINER,
            "BUILDER": cls.BUILDER,
            "SCOUT": cls.SCOUT,
            "RESEARCHER": cls.RESEARCHER,
            "TRADER": cls.TRADER
        }
        return color_map.get(specialization_name.upper(), cls.GENERALIST)
    
    @classmethod
    def get_terrain_color(cls, terrain_type: str) -> Tuple[int, int, int]:
        """Get color for a terrain type."""
        color_map = {
            "water": cls.WATER,
            "grass": cls.GRASS,
            "forest": cls.FOREST,
            "mountain": cls.MOUNTAIN,
            "desert": cls.DESERT,
            "snow": cls.SNOW
        }
        return color_map.get(terrain_type.lower(), cls.GRASS)
    
    @classmethod
    def lerp_color(cls, color1: Tuple[int, int, int], 
                   color2: Tuple[int, int, int], t: float) -> Tuple[int, int, int]:
        """Linear interpolation between two colors."""
        t = max(0.0, min(1.0, t))  # Clamp t to [0, 1]
        
        r = int(color1[0] + (color2[0] - color1[0]) * t)
        g = int(color1[1] + (color2[1] - color1[1]) * t)
        b = int(color1[2] + (color2[2] - color1[2]) * t)
        
        return (r, g, b)
    
    @classmethod
    def darken_color(cls, color: Tuple[int, int, int], factor: float) -> Tuple[int, int, int]:
        """Darken a color by the given factor."""
        factor = max(0.0, min(1.0, factor))
        return (
            int(color[0] * factor),
            int(color[1] * factor),
            int(color[2] * factor)
        )
    
    @classmethod
    def lighten_color(cls, color: Tuple[int, int, int], factor: float) -> Tuple[int, int, int]:
        """Lighten a color by the given factor."""
        factor = max(0.0, factor)
        return (
            min(255, int(color[0] * factor)),
            min(255, int(color[1] * factor)),
            min(255, int(color[2] * factor))
        )
