"""
Evolution and genetic drift system.
"""

import random
import logging
from typing import Dict, List, Any, Tuple

from ..game.config import CellConfig


class EvolutionSystem:
    """
    Manages genetic evolution and trait drift in cell populations.
    
    Handles natural selection, mutation, and environmental adaptation.
    """
    
    def __init__(self, config: CellConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Evolution tracking
        self.generation_count = 0
        self.trait_averages: Dict[str, float] = {}
        self.selection_pressures: Dict[str, float] = {}
        
        self.logger.info("Evolution system initialized")
    
    def update(self, delta_time: float, civilization: Any) -> None:
        """Update evolutionary processes."""
        if not hasattr(civilization, 'cells'):
            return
        
        # Update trait statistics
        self._update_trait_statistics(civilization.cells)
        
        # Apply environmental pressures
        self._apply_environmental_pressures(civilization.cells, delta_time)
        
        # Track generational changes
        self._track_generational_changes(civilization.cells)
    
    def _update_trait_statistics(self, cells: List[Any]) -> None:
        """Update average trait values across the population."""
        if not cells:
            return
        
        trait_sums = {}
        trait_counts = {}
        
        for cell in cells:
            for trait_name in cell.traits.__dataclass_fields__:
                trait_value = getattr(cell.traits, trait_name)
                trait_sums[trait_name] = trait_sums.get(trait_name, 0) + trait_value
                trait_counts[trait_name] = trait_counts.get(trait_name, 0) + 1
        
        # Calculate averages
        for trait_name in trait_sums:
            if trait_counts[trait_name] > 0:
                self.trait_averages[trait_name] = trait_sums[trait_name] / trait_counts[trait_name]
    
    def _apply_environmental_pressures(self, cells: List[Any], delta_time: float) -> None:
        """Apply environmental selection pressures."""
        # Placeholder: Apply simple selection pressures
        
        # Example: Cells with higher energy efficiency survive better in harsh conditions
        harsh_environment_factor = 0.1  # Could be based on world conditions
        
        for cell in cells:
            # Cells with poor energy efficiency lose more energy
            if cell.traits.energy_efficiency < 0.8:
                energy_penalty = harsh_environment_factor * delta_time * 10
                cell.energy = max(0, cell.energy - energy_penalty)
    
    def _track_generational_changes(self, cells: List[Any]) -> None:
        """Track changes across generations."""
        # Simple generation tracking based on average age
        if cells:
            average_age = sum(cell.age for cell in cells) / len(cells)
            
            # Consider it a new generation every 50 time units
            current_generation = int(average_age // 50)
            
            if current_generation > self.generation_count:
                self.generation_count = current_generation
                self.logger.info(f"Generation {self.generation_count} reached")
                self._log_evolutionary_progress()
    
    def _log_evolutionary_progress(self) -> None:
        """Log current evolutionary state."""
        if self.trait_averages:
            self.logger.info(f"Generation {self.generation_count} trait averages:")
            for trait, avg in self.trait_averages.items():
                self.logger.info(f"  {trait}: {avg:.3f}")
    
    def apply_mutation(self, cell: Any) -> None:
        """Apply mutation to a cell's traits."""
        mutation_rate = self.config.mutation_rate
        
        for trait_name in cell.traits.__dataclass_fields__:
            if random.random() < mutation_rate:
                current_value = getattr(cell.traits, trait_name)
                
                # Apply random mutation (±10%)
                mutation_factor = random.uniform(0.9, 1.1)
                new_value = max(0.01, current_value * mutation_factor)
                
                setattr(cell.traits, trait_name, new_value)
    
    def get_trait_diversity(self, cells: List[Any]) -> Dict[str, float]:
        """Calculate trait diversity in the population."""
        if not cells:
            return {}
        
        diversity = {}
        
        for trait_name in cells[0].traits.__dataclass_fields__:
            values = [getattr(cell.traits, trait_name) for cell in cells]
            
            # Calculate standard deviation as measure of diversity
            if len(values) > 1:
                mean = sum(values) / len(values)
                variance = sum((x - mean) ** 2 for x in values) / len(values)
                diversity[trait_name] = variance ** 0.5
            else:
                diversity[trait_name] = 0.0
        
        return diversity
    
    def get_fitness_distribution(self, cells: List[Any]) -> List[float]:
        """Calculate fitness scores for all cells."""
        fitness_scores = []
        
        for cell in cells:
            # Simple fitness calculation based on energy and age
            fitness = (cell.energy / cell.max_energy) * (1.0 + cell.age * 0.01)
            fitness_scores.append(fitness)
        
        return fitness_scores
    
    def serialize(self) -> Dict[str, Any]:
        """Serialize evolution system state."""
        return {
            "generation_count": self.generation_count,
            "trait_averages": self.trait_averages,
            "selection_pressures": self.selection_pressures
        }
    
    def deserialize(self, data: Dict[str, Any]) -> None:
        """Restore evolution system from saved data."""
        self.generation_count = data["generation_count"]
        self.trait_averages = data["trait_averages"]
        self.selection_pressures = data["selection_pressures"]
