"""
Cell entity - the fundamental unit of civilization in Cellular Dominion.
"""

import random
import math
from enum import Enum
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
import uuid

from ..utils.math_utils import Vector2, distance


class CellSpecialization(Enum):
    """Cell specialization types with associated colors."""
    GENERALIST = ("generalist", (128, 128, 128))  # Gray
    MINER = ("miner", (255, 0, 0))               # Red
    BUILDER = ("builder", (0, 0, 255))           # Blue
    SCOUT = ("scout", (0, 255, 0))               # Green
    RESEARCHER = ("researcher", (128, 0, 128))    # Purple
    TRADER = ("trader", (255, 255, 0))           # Yellow
    
    def __init__(self, name: str, color: Tuple[int, int, int]):
        self.display_name = name
        self.color = color


@dataclass
class CellTraits:
    """Genetic traits that influence cell behavior."""
    energy_efficiency: float = 1.0      # How efficiently the cell uses energy
    movement_speed: float = 1.0         # Base movement speed multiplier
    reproduction_drive: float = 1.0     # Likelihood to reproduce
    innovation: float = 0.1             # Chance to discover new things
    social_tendency: float = 0.5        # Preference for group activities
    risk_tolerance: float = 0.5         # Willingness to explore dangerous areas
    specialization_drift: float = 0.05  # Rate of specialization change
    
    def mutate(self, mutation_rate: float) -> 'CellTraits':
        """Create a mutated copy of these traits."""
        new_traits = CellTraits()
        
        for attr_name in self.__dataclass_fields__:
            current_value = getattr(self, attr_name)
            
            if random.random() < mutation_rate:
                # Apply mutation (±10% of current value)
                mutation_factor = random.uniform(0.9, 1.1)
                new_value = max(0.01, current_value * mutation_factor)
                setattr(new_traits, attr_name, new_value)
            else:
                setattr(new_traits, attr_name, current_value)
        
        return new_traits


class Cell:
    """
    A single cell entity representing a citizen in the civilization.
    
    Cells are autonomous agents that follow simple rules but create
    emergent complexity through their interactions.
    """
    
    def __init__(self, position: Vector2, traits: Optional[CellTraits] = None):
        self.id = str(uuid.uuid4())
        self.position = position
        self.velocity = Vector2(0, 0)
        
        # Core attributes
        self.energy = 100.0
        self.max_energy = 100.0
        self.age = 0.0
        self.loyalty = 1.0  # Loyalty to civilization
        
        # Genetic traits
        self.traits = traits or CellTraits()
        
        # Specialization
        self.specialization = CellSpecialization.GENERALIST
        self.specialization_strength = 0.0  # How specialized (0-1)
        
        # Behavioral state
        self.current_task = None
        self.target_position: Optional[Vector2] = None
        self.memory: Dict[str, Any] = {}
        
        # Social connections
        self.nearby_cells: List['Cell'] = []
        self.faction_id: Optional[str] = None
        
        # Experience and learning
        self.experience_points = 0
        self.discoveries: List[str] = []
        
    def update(self, delta_time: float, world_data: Any) -> None:
        """Update the cell's state and behavior."""
        self.age += delta_time
        
        # Energy consumption based on activity
        energy_cost = self._calculate_energy_cost(delta_time)
        self.energy = max(0, self.energy - energy_cost)
        
        # Update specialization drift
        self._update_specialization(delta_time)
        
        # Behavioral updates
        self._update_behavior(delta_time, world_data)
        self._update_movement(delta_time)
        
        # Check for reproduction
        if self._should_reproduce():
            return self._attempt_reproduction()
        
        return None
    
    def _calculate_energy_cost(self, delta_time: float) -> float:
        """Calculate energy consumption for this update."""
        base_cost = 1.0 * delta_time
        movement_cost = self.velocity.magnitude() * 0.5 * delta_time
        
        # Efficiency based on traits
        total_cost = (base_cost + movement_cost) / self.traits.energy_efficiency
        
        return total_cost
    
    def _update_specialization(self, delta_time: float) -> None:
        """Update cell specialization based on activities and environment."""
        drift_rate = self.traits.specialization_drift * delta_time
        
        # Specialization drifts based on recent activities
        # This is a simplified version - in full implementation,
        # this would be based on actual activities performed
        
        if random.random() < drift_rate:
            # Randomly drift toward a specialization
            specializations = list(CellSpecialization)
            weights = self._calculate_specialization_weights()
            
            chosen_spec = random.choices(specializations, weights=weights)[0]
            
            if chosen_spec != self.specialization:
                self.specialization = chosen_spec
                self.specialization_strength = min(1.0, self.specialization_strength + 0.1)
    
    def _calculate_specialization_weights(self) -> List[float]:
        """Calculate weights for specialization drift based on environment and traits."""
        # This would be much more sophisticated in the full implementation
        # For now, return equal weights with slight bias toward current specialization
        weights = [1.0] * len(CellSpecialization)
        
        # Slight bias toward current specialization
        current_index = list(CellSpecialization).index(self.specialization)
        weights[current_index] *= 1.2
        
        return weights
    
    def _update_behavior(self, delta_time: float, world_data: Any) -> None:
        """Update behavioral state and decision making."""
        # Simple behavior: move toward resources or other cells
        if not self.target_position:
            self._choose_new_target(world_data)
        
        # Update task based on specialization
        self._update_current_task()
    
    def _choose_new_target(self, world_data: Any) -> None:
        """Choose a new target position based on specialization and environment."""
        # Simplified target selection
        # In full implementation, this would analyze the world for resources,
        # other cells, threats, etc.
        
        search_radius = 50.0 + (self.traits.risk_tolerance * 50.0)
        
        # Random walk with bias toward specialization goals
        angle = random.uniform(0, 2 * math.pi)
        distance = random.uniform(10.0, search_radius)
        
        self.target_position = Vector2(
            self.position.x + math.cos(angle) * distance,
            self.position.y + math.sin(angle) * distance
        )
    
    def _update_current_task(self) -> None:
        """Update the current task based on specialization."""
        task_map = {
            CellSpecialization.MINER: "mining",
            CellSpecialization.BUILDER: "building",
            CellSpecialization.SCOUT: "exploring",
            CellSpecialization.RESEARCHER: "researching",
            CellSpecialization.TRADER: "trading",
            CellSpecialization.GENERALIST: "general_work"
        }
        
        self.current_task = task_map.get(self.specialization, "idle")
    
    def _update_movement(self, delta_time: float) -> None:
        """Update position based on target and movement traits."""
        if not self.target_position:
            return
        
        # Calculate direction to target
        direction = self.target_position - self.position
        distance_to_target = direction.magnitude()
        
        if distance_to_target < 2.0:
            # Reached target
            self.target_position = None
            self.velocity = Vector2(0, 0)
            return
        
        # Move toward target
        direction = direction.normalized()
        speed = 20.0 * self.traits.movement_speed
        
        self.velocity = direction * speed
        self.position += self.velocity * delta_time
    
    def _should_reproduce(self) -> bool:
        """Check if the cell should attempt reproduction."""
        energy_threshold = self.max_energy * 0.8
        age_threshold = 10.0  # Minimum age for reproduction
        
        return (self.energy >= energy_threshold and 
                self.age >= age_threshold and
                random.random() < self.traits.reproduction_drive * 0.001)
    
    def _attempt_reproduction(self) -> Optional['Cell']:
        """Attempt to create offspring."""
        if self.energy < self.max_energy * 0.6:
            return None
        
        # Create offspring with mutated traits
        offspring_traits = self.traits.mutate(0.05)  # 5% mutation rate
        
        # Position offspring nearby
        angle = random.uniform(0, 2 * math.pi)
        distance = random.uniform(5.0, 15.0)
        offspring_position = Vector2(
            self.position.x + math.cos(angle) * distance,
            self.position.y + math.sin(angle) * distance
        )
        
        offspring = Cell(offspring_position, offspring_traits)
        offspring.specialization = self.specialization  # Inherit specialization
        offspring.specialization_strength = max(0.1, self.specialization_strength * 0.8)
        
        # Cost of reproduction
        self.energy *= 0.7
        
        return offspring
    
    def interact_with_cell(self, other: 'Cell') -> None:
        """Handle interaction with another cell."""
        # Simple interaction: share some energy if other is low
        if other.energy < 20.0 and self.energy > 60.0:
            transfer_amount = min(10.0, self.energy - 50.0)
            self.energy -= transfer_amount
            other.energy += transfer_amount
    
    def get_color(self) -> Tuple[int, int, int]:
        """Get the display color for this cell."""
        base_color = self.specialization.color
        
        # Modify color based on energy level
        energy_factor = self.energy / self.max_energy
        
        return (
            int(base_color[0] * energy_factor),
            int(base_color[1] * energy_factor),
            int(base_color[2] * energy_factor)
        )
    
    def serialize(self) -> Dict[str, Any]:
        """Serialize cell data for saving."""
        return {
            "id": self.id,
            "position": {"x": self.position.x, "y": self.position.y},
            "energy": self.energy,
            "age": self.age,
            "specialization": self.specialization.name,
            "traits": {
                field.name: getattr(self.traits, field.name)
                for field in self.traits.__dataclass_fields__.values()
            }
        }
    
    @classmethod
    def deserialize(cls, data: Dict[str, Any]) -> 'Cell':
        """Create a cell from serialized data."""
        position = Vector2(data["position"]["x"], data["position"]["y"])
        
        # Reconstruct traits
        traits = CellTraits()
        for name, value in data["traits"].items():
            setattr(traits, name, value)
        
        cell = cls(position, traits)
        cell.id = data["id"]
        cell.energy = data["energy"]
        cell.age = data["age"]
        cell.specialization = CellSpecialization[data["specialization"]]
        
        return cell
