"""
Game state management system.
"""

from enum import Enum
from typing import Dict, Any
import logging


class GameState(Enum):
    """Possible game states."""
    MENU = "menu"
    PLAYING = "playing"
    PAUSED = "paused"
    LOADING = "loading"
    SAVING = "saving"


class GameStateManager:
    """
    Manages the overall state of the game.
    
    Handles state transitions, pause/resume functionality,
    and provides state information to other systems.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._current_state = GameState.MENU
        self._previous_state = None
        self._paused = False
        self._state_data: Dict[str, Any] = {}
        
        self.logger.info("Game state manager initialized")
    
    def get_current_state(self) -> GameState:
        """Get the current game state."""
        return self._current_state
    
    def get_previous_state(self) -> GameState:
        """Get the previous game state."""
        return self._previous_state
    
    def is_paused(self) -> bool:
        """Check if the game is paused."""
        return self._paused or self._current_state == GameState.PAUSED
    
    def set_state(self, new_state: GameState) -> None:
        """Change the game state."""
        if new_state == self._current_state:
            return
        
        self.logger.info(f"State transition: {self._current_state.value} -> {new_state.value}")
        
        self._previous_state = self._current_state
        self._current_state = new_state
        
        # Handle state-specific logic
        self._on_state_enter(new_state)
    
    def toggle_pause(self) -> None:
        """Toggle pause state."""
        if self._current_state == GameState.PLAYING:
            self._paused = not self._paused
            self.logger.info(f"Game {'paused' if self._paused else 'resumed'}")
        elif self._current_state == GameState.PAUSED:
            self.set_state(GameState.PLAYING)
    
    def start_game(self) -> None:
        """Start a new game."""
        self.set_state(GameState.PLAYING)
        self._paused = False
    
    def return_to_menu(self) -> None:
        """Return to the main menu."""
        self.set_state(GameState.MENU)
        self._paused = False
    
    def set_state_data(self, key: str, value: Any) -> None:
        """Store state-specific data."""
        self._state_data[key] = value
    
    def get_state_data(self, key: str, default: Any = None) -> Any:
        """Retrieve state-specific data."""
        return self._state_data.get(key, default)
    
    def clear_state_data(self) -> None:
        """Clear all state data."""
        self._state_data.clear()
    
    def _on_state_enter(self, state: GameState) -> None:
        """Handle state entry logic."""
        if state == GameState.PLAYING:
            self._paused = False
        elif state == GameState.MENU:
            self.clear_state_data()
        elif state == GameState.LOADING:
            self.set_state_data("loading_start_time", None)  # Would set actual time
        elif state == GameState.SAVING:
            self.set_state_data("saving_start_time", None)  # Would set actual time
    
    def can_transition_to(self, target_state: GameState) -> bool:
        """Check if transition to target state is valid."""
        current = self._current_state
        
        # Define valid transitions
        valid_transitions = {
            GameState.MENU: [GameState.PLAYING, GameState.LOADING],
            GameState.PLAYING: [GameState.PAUSED, GameState.MENU, GameState.SAVING],
            GameState.PAUSED: [GameState.PLAYING, GameState.MENU, GameState.SAVING],
            GameState.LOADING: [GameState.PLAYING, GameState.MENU],
            GameState.SAVING: [GameState.PLAYING, GameState.PAUSED, GameState.MENU]
        }
        
        return target_state in valid_transitions.get(current, [])
    
    def get_debug_info(self) -> Dict[str, Any]:
        """Get debug information about the state manager."""
        return {
            "current_state": self._current_state.value,
            "previous_state": self._previous_state.value if self._previous_state else None,
            "paused": self._paused,
            "state_data_keys": list(self._state_data.keys())
        }
