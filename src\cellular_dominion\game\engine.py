"""
Main game engine that orchestrates all game systems.
"""

import logging
import time
from typing import Optional

import pygame

from .config import GameConfig
from .state_manager import GameStateManager
from ..rendering.renderer import Renderer
from ..rendering.camera import Camera
from ..input.handler import InputHandler
from ..world.generator import WorldGenerator
from ..entities.civilization import Civilization
from ..systems.emergence import EmergenceSystem
from ..systems.evolution import EvolutionSystem


class GameEngine:
    """
    Main game engine that coordinates all game systems.
    
    Manages the game loop, system updates, and overall game state.
    """
    
    def __init__(self, config: GameConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.running = False
        self.clock = pygame.time.Clock()
        
        # Initialize pygame
        pygame.init()
        
        # Core systems
        self.state_manager = GameStateManager()
        self.camera = Camera(config.display.width, config.display.height)
        self.renderer = Renderer(config, self.camera)
        self.input_handler = InputHandler()
        
        # Game world and entities
        self.world_generator = WorldGenerator(config.world)
        self.civilization = Civilization(config.cells)
        
        # Emergent systems
        self.emergence_system = EmergenceSystem(config.emergence)
        self.evolution_system = EvolutionSystem(config.cells)
        
        # Timing
        self.last_update_time = 0.0
        self.last_save_time = 0.0
        
        self.logger.info("Game engine initialized")
    
    def run(self) -> None:
        """Main game loop."""
        self.logger.info("Starting game loop")
        self.running = True
        
        try:
            while self.running:
                current_time = time.time()
                delta_time = current_time - self.last_update_time
                self.last_update_time = current_time
                
                # Handle events
                self._handle_events()
                
                # Update game systems
                self._update(delta_time)
                
                # Render
                self._render()
                
                # Auto-save
                self._handle_auto_save(current_time)
                
                # Maintain frame rate
                self.clock.tick(self.config.display.fps_limit)
                
        except Exception as e:
            self.logger.error(f"Game loop error: {e}", exc_info=True)
            raise
        finally:
            self._cleanup()
    
    def _handle_events(self) -> None:
        """Process pygame events."""
        events = pygame.event.get()
        
        for event in events:
            if event.type == pygame.QUIT:
                self.running = False
            
            # Pass events to input handler
            self.input_handler.handle_event(event)
        
        # Process input commands
        commands = self.input_handler.get_commands()
        for command in commands:
            self._execute_command(command)
    
    def _execute_command(self, command: dict) -> None:
        """Execute a game command from input."""
        command_type = command.get("type")
        
        if command_type == "quit":
            self.running = False
        elif command_type == "camera_move":
            self.camera.move(command["dx"], command["dy"])
        elif command_type == "camera_zoom":
            self.camera.zoom(command["factor"])
        elif command_type == "toggle_pause":
            self.state_manager.toggle_pause()
        elif command_type == "save_game":
            self._save_game()
        elif command_type == "load_game":
            self._load_game()
    
    def _update(self, delta_time: float) -> None:
        """Update all game systems."""
        if self.state_manager.is_paused():
            return
        
        # Apply simulation speed
        effective_delta = delta_time * self.config.gameplay.simulation_speed
        
        # Update world (generate new chunks if needed)
        visible_chunks = self.camera.get_visible_chunks(self.config.world.chunk_size)
        self.world_generator.update(visible_chunks)
        
        # Update civilization
        self.civilization.update(effective_delta, self.world_generator.world)
        
        # Update emergent systems
        self.emergence_system.update(effective_delta, self.civilization)
        self.evolution_system.update(effective_delta, self.civilization)
        
        # Update camera
        self.camera.update(delta_time)
    
    def _render(self) -> None:
        """Render the game."""
        self.renderer.begin_frame()
        
        # Render world
        self.renderer.render_world(self.world_generator.world)
        
        # Render civilization
        self.renderer.render_civilization(self.civilization)
        
        # Render UI
        self.renderer.render_ui(self.state_manager, self.civilization)
        
        # Show FPS if enabled
        if self.config.display.show_fps:
            fps = self.clock.get_fps()
            self.renderer.render_fps(fps)
        
        self.renderer.end_frame()
    
    def _handle_auto_save(self, current_time: float) -> None:
        """Handle automatic saving."""
        if (current_time - self.last_save_time) >= self.config.gameplay.auto_save_interval:
            self._save_game()
            self.last_save_time = current_time
    
    def _save_game(self) -> None:
        """Save the current game state."""
        try:
            save_data = {
                "civilization": self.civilization.serialize(),
                "world": self.world_generator.serialize(),
                "camera": self.camera.serialize(),
                "timestamp": time.time()
            }
            
            # TODO: Implement actual save system
            self.logger.info("Game saved (placeholder)")
            
        except Exception as e:
            self.logger.error(f"Failed to save game: {e}")
    
    def _load_game(self) -> None:
        """Load a saved game state."""
        try:
            # TODO: Implement actual load system
            self.logger.info("Game loaded (placeholder)")
            
        except Exception as e:
            self.logger.error(f"Failed to load game: {e}")
    
    def _cleanup(self) -> None:
        """Clean up resources."""
        self.logger.info("Cleaning up game engine")
        pygame.quit()
