# Cellular Dominion Gameplay Guide

## Core Concept

Cellular Dominion is an emergent civilization-building game where you manage a growing society of autonomous "cells" on an infinite, procedurally-generated world. Each citizen is represented as a colored circle with simple behavioral rules that create emergent complexity through their interactions.

## Getting Started

### Basic Controls

- **WASD** or **Arrow Keys**: Move camera
- **Mouse Wheel**: Zoom in/out
- **Space**: Pause/Resume simulation
- **Ctrl+S**: Save game
- **Ctrl+L**: Load game
- **ESC**: Quit game

### Understanding Cells

Each cell in your civilization is an autonomous agent with:

- **Energy**: Life force that depletes over time
- **Age**: How long the cell has been alive
- **Specialization**: What role the cell performs
- **Traits**: Genetic characteristics that influence behavior

### Cell Specializations

Cells naturally drift toward different specializations:

- **Generalist** (Gray): Jack-of-all-trades, no specific focus
- **Miner** (Red): Extracts resources from the environment
- **Builder** (Blue): Constructs buildings and infrastructure
- **Scout** (Green): Explores new territories
- **Researcher** (Purple): Drives technological advancement
- **Trader** (Yellow): Facilitates resource exchange

## Emergent Systems

### Technology Discovery

Unlike traditional tech trees, technologies emerge naturally from cell interactions:

- Cells with high innovation near specific resources may discover new capabilities
- Technologies spread through your population organically
- Each discovery unlocks new cell behaviors and building types

### Cultural Evolution

Your civilization develops unique traits based on your choices:

- **Aggressive expansion** → Military culture → Stronger but resource-hungry cells
- **Peaceful trading** → Diplomatic culture → Cells that attract neutral NPCs
- **Research focus** → Academic culture → Faster discovery rates

### Genetic Drift

Cell populations evolve over generations:

- Successful traits become more common in offspring
- Environmental pressures shape evolution
- Mutations occasionally introduce new capabilities

## World Exploration

### Infinite World

The world generates procedurally as you explore:

- Different biomes with unique challenges and opportunities
- Resource nodes scattered throughout the landscape
- Terrain affects cell movement and behavior

### Resource Management

Resources are discovered and managed by your cells:

- **Food**: Sustains cell populations
- **Wood**: Used for construction
- **Stone**: Building material for advanced structures
- **Metal**: Required for complex technologies
- **Energy**: Powers advanced systems

## Advanced Gameplay

### Population Management

As your civilization grows:

- Monitor population health and energy levels
- Balance specializations for optimal efficiency
- Manage reproduction rates and genetic diversity

### Environmental Adaptation

Adapt to changing conditions:

- Different biomes favor different strategies
- Climate changes affect resource availability
- Natural disasters test civilization resilience

### Emergent Challenges

The game creates its own challenges:

- Rival civilizations with evolving strategies
- Internal politics and faction formation
- Resource depletion and environmental changes

## Tips for Success

### Early Game

1. Focus on basic resource gathering
2. Maintain population health and energy
3. Explore nearby areas for resources
4. Allow natural specialization to occur

### Mid Game

1. Encourage technological discovery
2. Expand territory strategically
3. Develop cultural identity
4. Manage population growth

### Late Game

1. Handle complex political dynamics
2. Adapt to environmental changes
3. Compete with advanced AI civilizations
4. Maintain long-term sustainability

## Understanding Emergence

The key to Cellular Dominion is understanding that complexity emerges from simplicity:

- Don't try to micromanage every cell
- Observe patterns and adapt your strategy
- Let the simulation surprise you
- Embrace unexpected developments

## Troubleshooting

### Performance Issues

- Reduce simulation speed if the game runs slowly
- Lower the maximum cell population
- Decrease render distance

### Population Problems

- If cells are dying, check energy levels and resource availability
- Ensure balanced specializations
- Monitor for environmental pressures

### Stagnation

- If nothing interesting is happening, try:
  - Expanding to new biomes
  - Encouraging specific specializations
  - Waiting for generational changes
