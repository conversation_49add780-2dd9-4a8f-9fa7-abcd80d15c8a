"""
Emergent behavior system for technology discovery and complex behaviors.
"""

import random
import logging
from typing import Dict, List, Any

from ..game.config import EmergenceConfig


class EmergenceSystem:
    """
    Manages emergent behaviors and technology discovery.
    
    Analyzes cell interactions to generate new technologies,
    behaviors, and civilization features.
    """
    
    def __init__(self, config: EmergenceConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Discovery tracking
        self.discovery_attempts = 0
        self.successful_discoveries = 0
        
        # Emergent patterns
        self.observed_patterns: Dict[str, int] = {}
        
        self.logger.info("Emergence system initialized")
    
    def update(self, delta_time: float, civilization: Any) -> None:
        """Update emergent behavior analysis."""
        # Analyze cell interactions for emergent patterns
        self._analyze_cell_interactions(civilization)
        
        # Check for technology discoveries
        self._check_technology_discovery(civilization)
        
        # Update emergent behaviors
        self._update_emergent_behaviors(civilization, delta_time)
    
    def _analyze_cell_interactions(self, civilization: Any) -> None:
        """Analyze interactions between cells for patterns."""
        if not hasattr(civilization, 'cells'):
            return
        
        # Placeholder: Count specialization clusters
        specialization_counts = {}
        for cell in civilization.cells:
            spec = cell.specialization.name
            specialization_counts[spec] = specialization_counts.get(spec, 0) + 1
        
        # Look for interesting patterns
        total_cells = len(civilization.cells)
        if total_cells > 0:
            for spec, count in specialization_counts.items():
                ratio = count / total_cells
                pattern_key = f"specialization_{spec}_ratio"
                
                # Track significant specialization ratios
                if ratio > 0.3:  # More than 30% of population
                    self.observed_patterns[pattern_key] = self.observed_patterns.get(pattern_key, 0) + 1
    
    def _check_technology_discovery(self, civilization: Any) -> None:
        """Check if conditions are met for technology discovery."""
        if not hasattr(civilization, 'cells'):
            return
        
        self.discovery_attempts += 1
        
        # Simple discovery chance based on innovation
        total_innovation = sum(cell.traits.innovation for cell in civilization.cells)
        discovery_chance = total_innovation * self.config.technology_discovery_rate
        
        if random.random() < discovery_chance:
            self._trigger_discovery(civilization)
    
    def _trigger_discovery(self, civilization: Any) -> None:
        """Trigger a technology discovery."""
        self.successful_discoveries += 1
        
        # Placeholder: Add a random technology
        technologies = ["Basic Tools", "Fire Making", "Agriculture", "Writing", "Mathematics"]
        
        if hasattr(civilization, 'technologies'):
            available = [tech for tech in technologies if tech not in civilization.technologies]
            if available:
                new_tech = random.choice(available)
                civilization.technologies.append(new_tech)
                self.logger.info(f"Technology discovered: {new_tech}")
    
    def _update_emergent_behaviors(self, civilization: Any, delta_time: float) -> None:
        """Update emergent behaviors based on observed patterns."""
        # Placeholder: Modify cell behaviors based on patterns
        
        # Example: If many miners, increase mining efficiency
        if "specialization_MINER_ratio" in self.observed_patterns:
            miner_pattern_strength = self.observed_patterns["specialization_MINER_ratio"]
            
            if miner_pattern_strength > 10:  # Pattern observed many times
                # Boost mining efficiency for all miners
                if hasattr(civilization, 'cells'):
                    for cell in civilization.cells:
                        if cell.specialization.name == "MINER":
                            cell.traits.energy_efficiency *= 1.01  # Small boost
    
    def get_discovery_rate(self) -> float:
        """Get current discovery success rate."""
        if self.discovery_attempts > 0:
            return self.successful_discoveries / self.discovery_attempts
        return 0.0
    
    def get_patterns(self) -> Dict[str, int]:
        """Get observed emergent patterns."""
        return self.observed_patterns.copy()
    
    def serialize(self) -> Dict[str, Any]:
        """Serialize emergence system state."""
        return {
            "discovery_attempts": self.discovery_attempts,
            "successful_discoveries": self.successful_discoveries,
            "observed_patterns": self.observed_patterns
        }
    
    def deserialize(self, data: Dict[str, Any]) -> None:
        """Restore emergence system from saved data."""
        self.discovery_attempts = data["discovery_attempts"]
        self.successful_discoveries = data["successful_discoveries"]
        self.observed_patterns = data["observed_patterns"]
