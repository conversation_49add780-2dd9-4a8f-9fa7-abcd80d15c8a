"""
Tests for emergent behavior systems.
"""

import pytest
from cellular_dominion.systems.emergence import EmergenceSystem
from cellular_dominion.systems.evolution import EvolutionSystem
from cellular_dominion.entities.civilization import Civilization
from cellular_dominion.game.config import EmergenceConfig, CellConfig


class TestEmergenceSystem:
    """Test cases for EmergenceSystem class."""
    
    def test_emergence_system_creation(self):
        """Test basic emergence system creation."""
        config = EmergenceConfig()
        system = EmergenceSystem(config)
        
        assert system.config == config
        assert system.discovery_attempts == 0
        assert system.successful_discoveries == 0
    
    def test_pattern_detection(self):
        """Test pattern detection in cell populations."""
        config = EmergenceConfig()
        system = EmergenceSystem(config)
        
        # Create a civilization with specific patterns
        cell_config = CellConfig()
        civilization = Civilization(cell_config)
        
        # Update the system
        system.update(1.0, civilization)
        
        # Should have analyzed the civilization
        assert system.discovery_attempts >= 0
    
    def test_discovery_tracking(self):
        """Test technology discovery tracking."""
        config = EmergenceConfig(technology_discovery_rate=1.0)  # High rate for testing
        system = EmergenceSystem(config)
        
        cell_config = CellConfig()
        civilization = Civilization(cell_config)
        
        # Update multiple times to potentially trigger discoveries
        initial_discoveries = system.successful_discoveries
        
        for _ in range(100):
            system.update(0.1, civilization)
        
        # Should have made some discovery attempts
        assert system.discovery_attempts > 0
        
        # Discovery rate should be calculable
        rate = system.get_discovery_rate()
        assert 0.0 <= rate <= 1.0


class TestEvolutionSystem:
    """Test cases for EvolutionSystem class."""
    
    def test_evolution_system_creation(self):
        """Test basic evolution system creation."""
        config = CellConfig()
        system = EvolutionSystem(config)
        
        assert system.config == config
        assert system.generation_count == 0
    
    def test_trait_statistics(self):
        """Test trait statistics calculation."""
        config = CellConfig()
        system = EvolutionSystem(config)
        
        cell_config = CellConfig()
        civilization = Civilization(cell_config)
        
        # Update the system
        system.update(1.0, civilization)
        
        # Should have calculated trait averages
        if civilization.cells:
            assert len(system.trait_averages) > 0
    
    def test_fitness_calculation(self):
        """Test fitness score calculation."""
        config = CellConfig()
        system = EvolutionSystem(config)
        
        cell_config = CellConfig()
        civilization = Civilization(cell_config)
        
        if civilization.cells:
            fitness_scores = system.get_fitness_distribution(civilization.cells)
            
            assert len(fitness_scores) == len(civilization.cells)
            assert all(score >= 0 for score in fitness_scores)
    
    def test_trait_diversity(self):
        """Test trait diversity calculation."""
        config = CellConfig()
        system = EvolutionSystem(config)
        
        cell_config = CellConfig()
        civilization = Civilization(cell_config)
        
        if civilization.cells:
            diversity = system.get_trait_diversity(civilization.cells)
            
            assert isinstance(diversity, dict)
            assert all(value >= 0 for value in diversity.values())


if __name__ == "__main__":
    pytest.main([__file__])
