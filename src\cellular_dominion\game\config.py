"""
Game configuration management using Pydantic for validation.
"""

import json
from pathlib import Path
from typing import Optional
from pydantic import BaseModel, Field


class DisplayConfig(BaseModel):
    """Display and rendering configuration."""
    width: int = Field(default=1280, ge=800, le=3840)
    height: int = Field(default=720, ge=600, le=2160)
    fullscreen: bool = False
    vsync: bool = True
    fps_limit: int = Field(default=60, ge=30, le=144)
    show_fps: bool = False


class GameplayConfig(BaseModel):
    """Core gameplay configuration."""
    initial_cells: int = Field(default=10, ge=1, le=100)
    world_seed: Optional[int] = None
    simulation_speed: float = Field(default=1.0, ge=0.1, le=5.0)
    auto_save_interval: int = Field(default=300, ge=60)  # seconds
    max_cells: int = Field(default=10000, ge=100)


class WorldConfig(BaseModel):
    """World generation configuration."""
    chunk_size: int = Field(default=64, ge=16, le=256)
    render_distance: int = Field(default=3, ge=1, le=10)  # chunks
    biome_scale: float = Field(default=0.01, ge=0.001, le=0.1)
    resource_density: float = Field(default=0.3, ge=0.1, le=1.0)


class CellConfig(BaseModel):
    """Cell behavior configuration."""
    max_energy: int = Field(default=100, ge=50, le=500)
    reproduction_threshold: int = Field(default=80, ge=50, le=100)
    mutation_rate: float = Field(default=0.01, ge=0.0, le=0.1)
    specialization_drift: float = Field(default=0.05, ge=0.0, le=0.2)


class EmergenceConfig(BaseModel):
    """Emergent behavior configuration."""
    technology_discovery_rate: float = Field(default=0.001, ge=0.0, le=0.01)
    cultural_evolution_rate: float = Field(default=0.002, ge=0.0, le=0.02)
    faction_formation_threshold: int = Field(default=50, ge=10, le=200)


class GameConfig(BaseModel):
    """Main game configuration container."""
    display: DisplayConfig = Field(default_factory=DisplayConfig)
    gameplay: GameplayConfig = Field(default_factory=GameplayConfig)
    world: WorldConfig = Field(default_factory=WorldConfig)
    cells: CellConfig = Field(default_factory=CellConfig)
    emergence: EmergenceConfig = Field(default_factory=EmergenceConfig)
    
    def save_to_file(self, path: Path) -> None:
        """Save configuration to JSON file."""
        with open(path, 'w') as f:
            json.dump(self.model_dump(), f, indent=2)
    
    def load_from_file(self, path: Path) -> None:
        """Load configuration from JSON file."""
        with open(path, 'r') as f:
            data = json.load(f)
        
        # Update current instance with loaded data
        loaded_config = GameConfig.model_validate(data)
        for field_name, field_value in loaded_config.model_dump().items():
            setattr(self, field_name, field_value)
    
    @classmethod
    def create_default_config_file(cls, path: Path) -> None:
        """Create a default configuration file."""
        default_config = cls()
        default_config.save_to_file(path)
